#!/usr/bin/env python3
"""
Performance benchmark suite for DOAXVV Text Recognition System.

This module provides comprehensive performance testing and benchmarking
for all components of the text recognition system.
"""

import time
import sys
import statistics
import psutil
import platform
from pathlib import Path
from typing import Dict, List, Any
import numpy as np
import cv2

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from doaxvv_ocr import DOAXVVTextRecognizer, Config
from doaxvv_ocr.capture.screen_capture import ScreenCaptureFactory
from doaxvv_ocr.ocr.ocr_manager import OCRManager
from doaxvv_ocr.preprocessing.image_processor import ImageProcessor
from doaxvv_ocr.postprocessing.text_processor import TextProcessor


class PerformanceBenchmark:
    """Comprehensive performance benchmark suite."""
    
    def __init__(self):
        """Initialize benchmark suite."""
        self.results = {}
        self.system_info = self._get_system_info()
        
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information for benchmark context."""
        return {
            "platform": platform.platform(),
            "processor": platform.processor(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total // (1024**3),  # GB
            "python_version": platform.python_version(),
        }
    
    def run_all_benchmarks(self) -> Dict[str, Any]:
        """Run all benchmark tests."""
        print("DOAXVV Text Recognition - Performance Benchmark")
        print("=" * 60)
        print(f"System: {self.system_info['platform']}")
        print(f"CPU: {self.system_info['processor']}")
        print(f"Cores: {self.system_info['cpu_count']}")
        print(f"Memory: {self.system_info['memory_total']}GB")
        print("=" * 60)
        
        # Run individual benchmarks
        self.results["screen_capture"] = self.benchmark_screen_capture()
        self.results["image_processing"] = self.benchmark_image_processing()
        self.results["text_processing"] = self.benchmark_text_processing()
        self.results["ocr_engines"] = self.benchmark_ocr_engines()
        self.results["end_to_end"] = self.benchmark_end_to_end()
        
        # Generate summary
        self.results["summary"] = self._generate_summary()
        self.results["system_info"] = self.system_info
        
        return self.results
    
    def benchmark_screen_capture(self) -> Dict[str, Any]:
        """Benchmark screen capture performance."""
        print("\n1. Screen Capture Benchmark")
        print("-" * 30)
        
        config = Config()
        results = {}
        
        try:
            capture = ScreenCaptureFactory.create(config.capture)
            
            with capture:
                # Warmup
                for _ in range(5):
                    capture.capture()
                
                # Benchmark
                times = []
                frame_sizes = []
                
                for i in range(50):
                    start_time = time.time()
                    frame = capture.capture()
                    end_time = time.time()
                    
                    if frame is not None:
                        times.append(end_time - start_time)
                        frame_sizes.append(frame.nbytes)
                    
                    if i % 10 == 0:
                        print(f"  Capture {i+1}/50...")
                
                if times:
                    results = {
                        "avg_time": statistics.mean(times),
                        "min_time": min(times),
                        "max_time": max(times),
                        "std_time": statistics.stdev(times) if len(times) > 1 else 0,
                        "fps": 1.0 / statistics.mean(times),
                        "avg_frame_size_mb": statistics.mean(frame_sizes) / (1024**2),
                        "success_rate": len(times) / 50
                    }
                    
                    print(f"  Average FPS: {results['fps']:.1f}")
                    print(f"  Average time: {results['avg_time']*1000:.1f}ms")
                    print(f"  Frame size: {results['avg_frame_size_mb']:.1f}MB")
                else:
                    results = {"error": "No successful captures"}
                    
        except Exception as e:
            results = {"error": str(e)}
            print(f"  Error: {e}")
        
        return results
    
    def benchmark_image_processing(self) -> Dict[str, Any]:
        """Benchmark image preprocessing performance."""
        print("\n2. Image Processing Benchmark")
        print("-" * 30)
        
        config = Config()
        processor = ImageProcessor(config.pipeline)
        
        # Create test images of different sizes
        test_images = {
            "small": np.random.randint(0, 255, (240, 320, 3), dtype=np.uint8),
            "medium": np.random.randint(0, 255, (720, 1280, 3), dtype=np.uint8),
            "large": np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8),
        }
        
        results = {}
        
        for size_name, image in test_images.items():
            print(f"  Testing {size_name} image ({image.shape[1]}x{image.shape[0]})...")
            
            times = []
            
            # Warmup
            for _ in range(3):
                processor.preprocess(image)
            
            # Benchmark
            for _ in range(20):
                start_time = time.time()
                processed = processor.preprocess(image)
                end_time = time.time()
                
                times.append(end_time - start_time)
            
            results[size_name] = {
                "avg_time": statistics.mean(times),
                "min_time": min(times),
                "max_time": max(times),
                "fps": 1.0 / statistics.mean(times),
                "image_size": f"{image.shape[1]}x{image.shape[0]}"
            }
            
            print(f"    Average time: {results[size_name]['avg_time']*1000:.1f}ms")
            print(f"    Max FPS: {results[size_name]['fps']:.1f}")
        
        return results
    
    def benchmark_text_processing(self) -> Dict[str, Any]:
        """Benchmark text postprocessing performance."""
        print("\n3. Text Processing Benchmark")
        print("-" * 30)
        
        config = Config()
        processor = TextProcessor(config.pipeline)
        
        # Test texts of varying complexity
        test_texts = [
            "Simple text",
            "Venus Points: 1500",
            "Gacha Event Limited Time Offer!",
            "複雑な日本語テキストの処理テスト",
            "Mixed English and 日本語 text with numbers 12345",
            "  Messy   text   with    extra    spaces   ",
            "Special characters: !@#$%^&*()_+-=[]{}|;:,.<>?",
        ]
        
        times = []
        
        print(f"  Processing {len(test_texts)} different text patterns...")
        
        # Warmup
        for text in test_texts:
            processor.process_text(text)
        
        # Benchmark
        for _ in range(100):
            for text in test_texts:
                start_time = time.time()
                processed = processor.process_text(text)
                end_time = time.time()
                
                times.append(end_time - start_time)
        
        results = {
            "avg_time": statistics.mean(times),
            "min_time": min(times),
            "max_time": max(times),
            "texts_per_second": 1.0 / statistics.mean(times),
            "total_tests": len(times)
        }
        
        print(f"  Average time: {results['avg_time']*1000:.3f}ms")
        print(f"  Texts per second: {results['texts_per_second']:.0f}")
        
        return results
    
    def benchmark_ocr_engines(self) -> Dict[str, Any]:
        """Benchmark OCR engine performance."""
        print("\n4. OCR Engine Benchmark")
        print("-" * 30)
        
        # Create test image with text
        test_image = np.ones((200, 600, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "DOAXVV Test Text", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 3)
        cv2.putText(test_image, "Venus Points: 1500", (50, 150), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        config = Config()
        results = {}
        
        try:
            ocr_manager = OCRManager(config.ocr)
            available_engines = ocr_manager.get_available_engines()
            
            print(f"  Available engines: {available_engines}")
            
            if available_engines:
                times = []
                accuracies = []
                
                # Warmup
                for _ in range(3):
                    ocr_manager.recognize(test_image)
                
                # Benchmark
                for i in range(20):
                    start_time = time.time()
                    result = ocr_manager.recognize(test_image)
                    end_time = time.time()
                    
                    times.append(end_time - start_time)
                    
                    if result:
                        # Simple accuracy check
                        if "DOAXVV" in result.text or "Test" in result.text:
                            accuracies.append(1.0)
                        else:
                            accuracies.append(0.0)
                    else:
                        accuracies.append(0.0)
                    
                    if i % 5 == 0:
                        print(f"    OCR test {i+1}/20...")
                
                results = {
                    "avg_time": statistics.mean(times),
                    "min_time": min(times),
                    "max_time": max(times),
                    "fps": 1.0 / statistics.mean(times),
                    "accuracy": statistics.mean(accuracies),
                    "available_engines": available_engines,
                    "engine_performance": ocr_manager.get_performance_metrics()
                }
                
                print(f"  Average time: {results['avg_time']*1000:.1f}ms")
                print(f"  Max FPS: {results['fps']:.1f}")
                print(f"  Accuracy: {results['accuracy']*100:.1f}%")
                
            else:
                results = {"error": "No OCR engines available"}
                print("  Error: No OCR engines could be initialized")
            
            ocr_manager.cleanup()
            
        except Exception as e:
            results = {"error": str(e)}
            print(f"  Error: {e}")
        
        return results
    
    def benchmark_end_to_end(self) -> Dict[str, Any]:
        """Benchmark complete end-to-end performance."""
        print("\n5. End-to-End Benchmark")
        print("-" * 30)
        
        try:
            recognizer = DOAXVVTextRecognizer()
            
            # Create test image
            test_image = np.ones((720, 1280, 3), dtype=np.uint8) * 240
            cv2.putText(test_image, "DOAXVV", (100, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
            cv2.putText(test_image, "Venus Points: 2500", (100, 200), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            cv2.putText(test_image, "Gacha", (100, 300), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 2)
            
            times = []
            text_counts = []
            
            print("  Running end-to-end recognition tests...")
            
            # Warmup
            for _ in range(3):
                recognizer.recognize_frame(test_image)
            
            # Benchmark
            for i in range(30):
                start_time = time.time()
                results = recognizer.recognize_frame(test_image)
                end_time = time.time()
                
                times.append(end_time - start_time)
                text_counts.append(len(results))
                
                if i % 5 == 0:
                    print(f"    Test {i+1}/30...")
            
            benchmark_results = {
                "avg_time": statistics.mean(times),
                "min_time": min(times),
                "max_time": max(times),
                "fps": 1.0 / statistics.mean(times),
                "avg_detections": statistics.mean(text_counts),
                "total_tests": len(times)
            }
            
            print(f"  Average time: {benchmark_results['avg_time']*1000:.1f}ms")
            print(f"  Max FPS: {benchmark_results['fps']:.1f}")
            print(f"  Avg detections: {benchmark_results['avg_detections']:.1f}")
            
        except Exception as e:
            benchmark_results = {"error": str(e)}
            print(f"  Error: {e}")
        
        return benchmark_results
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate benchmark summary."""
        summary = {
            "overall_performance": "Unknown",
            "bottlenecks": [],
            "recommendations": []
        }
        
        try:
            # Analyze end-to-end performance
            if "end_to_end" in self.results and "fps" in self.results["end_to_end"]:
                fps = self.results["end_to_end"]["fps"]
                
                if fps >= 25:
                    summary["overall_performance"] = "Excellent"
                elif fps >= 20:
                    summary["overall_performance"] = "Good"
                elif fps >= 15:
                    summary["overall_performance"] = "Acceptable"
                else:
                    summary["overall_performance"] = "Poor"
                
                # Identify bottlenecks
                if "screen_capture" in self.results and "fps" in self.results["screen_capture"]:
                    capture_fps = self.results["screen_capture"]["fps"]
                    if capture_fps < fps * 1.5:
                        summary["bottlenecks"].append("Screen capture")
                
                if "ocr_engines" in self.results and "fps" in self.results["ocr_engines"]:
                    ocr_fps = self.results["ocr_engines"]["fps"]
                    if ocr_fps < fps * 1.5:
                        summary["bottlenecks"].append("OCR processing")
                
                # Generate recommendations
                if fps < 15:
                    summary["recommendations"].extend([
                        "Enable GPU acceleration",
                        "Use ROI detection to process smaller areas",
                        "Reduce target FPS in configuration",
                        "Use faster OCR engine (PaddleOCR only)"
                    ])
                elif fps < 20:
                    summary["recommendations"].extend([
                        "Consider enabling GPU acceleration",
                        "Optimize ROI regions for your use case"
                    ])
        
        except Exception as e:
            summary["error"] = str(e)
        
        return summary
    
    def print_summary(self):
        """Print benchmark summary."""
        print("\n" + "=" * 60)
        print("BENCHMARK SUMMARY")
        print("=" * 60)
        
        if "summary" in self.results:
            summary = self.results["summary"]
            print(f"Overall Performance: {summary.get('overall_performance', 'Unknown')}")
            
            if summary.get("bottlenecks"):
                print(f"Bottlenecks: {', '.join(summary['bottlenecks'])}")
            
            if summary.get("recommendations"):
                print("Recommendations:")
                for rec in summary["recommendations"]:
                    print(f"  - {rec}")
        
        # Print key metrics
        if "end_to_end" in self.results and "fps" in self.results["end_to_end"]:
            fps = self.results["end_to_end"]["fps"]
            time_ms = self.results["end_to_end"]["avg_time"] * 1000
            print(f"\nKey Metrics:")
            print(f"  End-to-End FPS: {fps:.1f}")
            print(f"  Processing Time: {time_ms:.1f}ms")
            
            if fps >= 15:
                print("  ✅ Meets performance requirements (15+ FPS)")
            else:
                print("  ❌ Below performance requirements (15+ FPS)")
            
            if time_ms <= 100:
                print("  ✅ Meets processing time requirements (<100ms)")
            else:
                print("  ❌ Above processing time requirements (<100ms)")


def main():
    """Run performance benchmark."""
    benchmark = PerformanceBenchmark()
    
    try:
        results = benchmark.run_all_benchmarks()
        benchmark.print_summary()
        
        # Save results to file
        import json
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"benchmark_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nDetailed results saved to: {filename}")
        
    except KeyboardInterrupt:
        print("\nBenchmark interrupted by user")
    except Exception as e:
        print(f"\nBenchmark failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
