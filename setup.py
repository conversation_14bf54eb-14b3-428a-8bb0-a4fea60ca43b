#!/usr/bin/env python3
"""
Setup script for DOAXVV Real-Time Text Recognition System
"""

import os
import sys
import platform
from setuptools import setup, find_packages

# Read the README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

# Platform-specific dependencies
platform_deps = {
    "win32": [
        "pywin32>=306",
        "pygetwindow>=0.0.9",
    ],
    "darwin": [
        "pyobjc-framework-Quartz>=9.2",
        "pyobjc-framework-CoreGraphics>=9.2",
    ],
    "linux": [
        "python-xlib>=0.33",
        "pycairo>=1.24.0",
    ]
}

# Add platform-specific dependencies
current_platform = sys.platform
if current_platform in platform_deps:
    requirements.extend(platform_deps[current_platform])

# GPU dependencies (optional)
gpu_requirements = [
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "onnxruntime-gpu>=1.15.0",
]

# Development dependencies
dev_requirements = [
    "pytest>=7.4.0",
    "pytest-benchmark>=4.0.0",
    "pytest-cov>=4.1.0",
    "black>=23.7.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "memory-profiler>=0.61.0",
    "line-profiler>=4.1.0",
    "py-spy>=0.3.14",
]

setup(
    name="doaxvv-text-recognition",
    version="1.0.0",
    author="DOAXVV OCR Team",
    author_email="<EMAIL>",
    description="Real-time text recognition system for Dead or Alive Xtreme Venus Vacation",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/doaxvv-text-recognition",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Graphics :: Capture :: Screen Capture",
        "Topic :: Scientific/Engineering :: Image Recognition",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "gpu": gpu_requirements,
        "dev": dev_requirements,
        "all": gpu_requirements + dev_requirements,
    },
    entry_points={
        "console_scripts": [
            "doaxvv-ocr=doaxvv_ocr.cli:main",
            "doaxvv-benchmark=doaxvv_ocr.benchmark:main",
            "doaxvv-config=doaxvv_ocr.config.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "doaxvv_ocr": [
            "config/*.yaml",
            "models/*.onnx",
            "assets/*.png",
        ],
    },
    zip_safe=False,
)
