# DOAXVV Real-Time Text Recognition System

A high-performance, real-time text recognition system specifically designed for Dead or Alive Xtreme Venus Vacation (DOAXVV) with support for English and Japanese text recognition.

## Features

- **High Performance**: 15+ FPS text recognition with <100ms processing time
- **Multi-Language Support**: English and Japanese text recognition
- **Cross-Platform**: Windows 10/11, Linux, and macOS support
- **Hardware Flexibility**: CPU-only, NVIDIA GPU (CUDA), Apple Silicon (Metal)
- **Offline Operation**: Complete offline functionality with no cloud dependencies
- **Real-Time Monitoring**: Live screen capture and text detection
- **DOAXVV Optimized**: Specifically tuned for DOAXVV's UI elements and fonts

## Performance Requirements

- **Frame Rate**: 15+ FPS during active gameplay
- **Processing Time**: <100ms per frame
- **CPU Usage**: <50% during operation
- **Memory**: 8GB+ RAM minimum

## Accuracy Requirements

- **Overall Accuracy**: 95%+ on DOAXVV UI text elements
- **Language Support**: English and Japanese text recognition
- **UI Adaptation**: Handles DOAXVV fonts, layouts, and visual styles
- **Text Types**: Menus, overlays, notifications, and in-game text

## Architecture

### OCR Engines
1. **PaddleOCR** (Primary) - Fast multilingual inference
2. **EasyOCR** (Secondary) - GPU acceleration and Japanese support
3. **TesseractOCR** (Fallback) - Mature and reliable

### Text Detection Pipeline
```
Screen Capture → Preprocessing → Region Detection → OCR Processing → Post-processing → Output
```

### Screen Capture Methods
- **Windows**: DirectX/DXGI
- **macOS**: Quartz/CoreGraphics  
- **Linux**: X11/XCB

## Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd text_recognition

# Install dependencies
pip install -r requirements.txt

# Install platform-specific dependencies
python setup.py install
```

### Basic Usage

```python
from doaxvv_ocr import DOAXVVTextRecognizer

# Initialize the recognizer
recognizer = DOAXVVTextRecognizer()

# Start real-time recognition
recognizer.start_realtime_recognition()
```

## Project Structure

```
text_recognition/
├── src/
│   ├── core/                 # Core recognition engine
│   ├── capture/              # Screen capture implementations
│   ├── ocr/                  # OCR engine integrations
│   ├── preprocessing/        # Image preprocessing
│   ├── postprocessing/       # Text post-processing
│   ├── config/               # Configuration management
│   └── ui/                   # User interface
├── tests/                    # Test suite
├── benchmarks/               # Performance benchmarks
├── examples/                 # Usage examples
├── docs/                     # Documentation
└── assets/                   # Sample images and resources
```

## Hardware Compatibility

| Platform | CPU | GPU | Memory | Status |
|----------|-----|-----|--------|--------|
| Windows 10/11 | Intel/AMD x64 | NVIDIA CUDA | 8GB+ | ✅ |
| Windows 10/11 | Intel/AMD x64 | CPU Only | 8GB+ | ✅ |
| macOS | Intel x64 | CPU Only | 8GB+ | ✅ |
| macOS | Apple Silicon | Metal | 8GB+ | ✅ |
| Linux | Intel/AMD x64 | NVIDIA CUDA | 8GB+ | ✅ |
| Linux | Intel/AMD x64 | CPU Only | 8GB+ | ✅ |

## Performance Benchmarks

| Configuration | FPS | Processing Time | CPU Usage | Memory Usage |
|---------------|-----|----------------|-----------|--------------|
| RTX 3070 + CUDA | 25+ | 40ms | 30% | 2.1GB |
| Apple M1 + Metal | 20+ | 50ms | 35% | 1.8GB |
| Intel i7 CPU-only | 18+ | 55ms | 45% | 1.5GB |

## License

MIT License - see LICENSE file for details.

## Contributing

Please read CONTRIBUTING.md for details on our code of conduct and the process for submitting pull requests.
