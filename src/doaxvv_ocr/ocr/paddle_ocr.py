"""
PaddleOCR engine implementation for DOAXVV text recognition.

This module provides integration with PaddleOCR, which offers excellent
performance for multilingual text recognition with GPU acceleration support.
"""

import time
from typing import List, Optional, Dict, Any

import numpy as np
import cv2

from .base_ocr import BaseOCREngine, OCRResult, TextDetection


class PaddleOCREngine(BaseOCREngine):
    """PaddleOCR engine implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize PaddleOCR engine.
        
        Args:
            config: PaddleOCR configuration
        """
        super().__init__(config)
        self._ocr = None
        
        # Default configuration
        self._default_config = {
            "use_angle_cls": True,
            "lang": "en",
            "use_gpu": True,
            "show_log": False,
            "det_model_dir": None,
            "rec_model_dir": None,
            "cls_model_dir": None
        }
        
        # Merge with provided config
        self._ocr_config = {**self._default_config, **config}
    
    def initialize(self) -> bool:
        """
        Initialize PaddleOCR engine.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            from paddleocr import PaddleOCR
            
            # Initialize PaddleOCR
            self._ocr = PaddleOCR(**self._ocr_config)
            
            # Test with a small dummy image
            test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
            _ = self._ocr.ocr(test_image, cls=self._ocr_config.get("use_angle_cls", True))
            
            self._initialized = True
            self.logger.info("PaddleOCR engine initialized successfully")
            return True
            
        except ImportError:
            self.logger.error("PaddleOCR not installed. Install with: pip install paddlepaddle paddleocr")
            return False
        except Exception as e:
            self.logger.error(f"Failed to initialize PaddleOCR: {e}")
            return False
    
    def recognize(self, image: np.ndarray) -> Optional[OCRResult]:
        """
        Recognize text in image using PaddleOCR.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            OCR result or None if recognition failed
        """
        if not self._initialized or self._ocr is None:
            return None
        
        try:
            start_time = time.time()
            
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            # Run OCR
            results = self._ocr.ocr(processed_image, cls=self._ocr_config.get("use_angle_cls", True))
            
            if not results or not results[0]:
                return None
            
            # Combine all detected text
            all_text = []
            total_confidence = 0.0
            bbox_list = []
            
            for line in results[0]:
                if len(line) >= 2:
                    bbox = line[0]
                    text_info = line[1]
                    
                    if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                        text = text_info[0]
                        confidence = text_info[1]
                        
                        if text and confidence > 0.5:  # Minimum confidence threshold
                            all_text.append(text)
                            total_confidence += confidence
                            bbox_list.append(bbox)
            
            if not all_text:
                return None
            
            # Calculate combined result
            combined_text = " ".join(all_text)
            avg_confidence = total_confidence / len(all_text)
            
            # Calculate bounding box for all text
            if bbox_list:
                all_points = np.array([point for bbox in bbox_list for point in bbox])
                x_min, y_min = np.min(all_points, axis=0).astype(int)
                x_max, y_max = np.max(all_points, axis=0).astype(int)
                combined_bbox = (x_min, y_min, x_max - x_min, y_max - y_min)
            else:
                combined_bbox = None
            
            # Post-process text
            cleaned_text = self.postprocess_text(combined_text)
            
            processing_time = time.time() - start_time
            
            return OCRResult(
                text=cleaned_text,
                confidence=avg_confidence,
                bbox=combined_bbox,
                language=self._ocr_config.get("lang", "en"),
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"PaddleOCR recognition error: {e}")
            return None
    
    def detect_and_recognize(self, image: np.ndarray) -> List[TextDetection]:
        """
        Detect and recognize all text in image using PaddleOCR.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of text detections
        """
        if not self._initialized or self._ocr is None:
            return []
        
        try:
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            # Run OCR
            results = self._ocr.ocr(processed_image, cls=self._ocr_config.get("use_angle_cls", True))
            
            if not results or not results[0]:
                return []
            
            detections = []
            
            for line in results[0]:
                if len(line) >= 2:
                    bbox = line[0]
                    text_info = line[1]
                    
                    if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                        text = text_info[0]
                        confidence = text_info[1]
                        
                        if text and confidence > 0.3:  # Lower threshold for individual detections
                            # Convert bbox to (x, y, width, height)
                            points = np.array(bbox)
                            x_min, y_min = np.min(points, axis=0).astype(int)
                            x_max, y_max = np.max(points, axis=0).astype(int)
                            
                            detection_bbox = (x_min, y_min, x_max - x_min, y_max - y_min)
                            
                            # Post-process text
                            cleaned_text = self.postprocess_text(text)
                            
                            detection = TextDetection(
                                text=cleaned_text,
                                confidence=confidence,
                                bbox=detection_bbox,
                                language=self._ocr_config.get("lang", "en")
                            )
                            detections.append(detection)
            
            return detections
            
        except Exception as e:
            self.logger.error(f"PaddleOCR detection error: {e}")
            return []
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for better PaddleOCR results.
        
        Args:
            image: Input image
            
        Returns:
            Preprocessed image
        """
        # Ensure image is in correct format
        if len(image.shape) == 3 and image.shape[2] == 3:
            # Convert BGR to RGB for PaddleOCR
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Apply basic preprocessing
        # Resize if image is too small
        height, width = image.shape[:2]
        if height < 32 or width < 32:
            scale_factor = max(32 / height, 32 / width)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        return image
    
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported languages for PaddleOCR.
        
        Returns:
            List of language codes
        """
        return [
            "en", "ch", "ta", "te", "ka", "ja", "ko", "hi", "ar", "cyrillic",
            "devanagari", "fr", "german", "korean", "japan"
        ]
    
    def set_language(self, language: str) -> bool:
        """
        Set recognition language for PaddleOCR.
        
        Args:
            language: Language code
            
        Returns:
            True if successful, False otherwise
        """
        if language not in self.get_supported_languages():
            return False
        
        try:
            # Update configuration
            self._ocr_config["lang"] = language
            
            # Reinitialize with new language
            from paddleocr import PaddleOCR
            self._ocr = PaddleOCR(**self._ocr_config)
            
            self.logger.info(f"PaddleOCR language set to: {language}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set PaddleOCR language: {e}")
            return False
    
    def cleanup(self) -> None:
        """Clean up PaddleOCR resources."""
        if self._ocr:
            try:
                # PaddleOCR doesn't have explicit cleanup, but we can clear the reference
                self._ocr = None
                self.logger.info("PaddleOCR engine cleaned up")
            except Exception as e:
                self.logger.error(f"Error during PaddleOCR cleanup: {e}")
        
        self._initialized = False
