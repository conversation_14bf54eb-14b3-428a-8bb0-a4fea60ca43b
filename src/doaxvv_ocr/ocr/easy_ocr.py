"""
EasyOCR engine implementation for DOAXVV text recognition.

This module provides integration with EasyOCR, which offers good
Japanese text support and GPU acceleration.
"""

import time
from typing import List, Optional, Dict, Any

import numpy as np

from .base_ocr import BaseOCREngine, OCRResult, TextDetection


class EasyOCREngine(BaseOCREngine):
    """EasyOCR engine implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize EasyOCR engine.
        
        Args:
            config: EasyOCR configuration
        """
        super().__init__(config)
        self._reader = None
        
        # Default configuration
        self._default_config = {
            "languages": ["en", "ja"],
            "gpu": True,
            "verbose": False,
            "detector": True,
            "recognizer": True
        }
        
        # Merge with provided config
        self._easyocr_config = {**self._default_config, **config}
    
    def initialize(self) -> bool:
        """
        Initialize EasyOCR engine.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            import easyocr
            
            # Initialize EasyOCR reader
            self._reader = easyocr.Reader(
                self._easyocr_config["languages"],
                gpu=self._easyocr_config["gpu"],
                verbose=self._easyocr_config["verbose"]
            )
            
            self._initialized = True
            self.logger.info("EasyOCR engine initialized successfully")
            return True
            
        except ImportError:
            self.logger.error("EasyOCR not installed. Install with: pip install easyocr")
            return False
        except Exception as e:
            self.logger.error(f"Failed to initialize EasyOCR: {e}")
            return False
    
    def recognize(self, image: np.ndarray) -> Optional[OCRResult]:
        """
        Recognize text in image using EasyOCR.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            OCR result or None if recognition failed
        """
        if not self._initialized or self._reader is None:
            return None
        
        try:
            start_time = time.time()
            
            # Run OCR
            results = self._reader.readtext(image)
            
            if not results:
                return None
            
            # Combine all detected text
            all_text = []
            total_confidence = 0.0
            
            for result in results:
                if len(result) >= 3:
                    bbox, text, confidence = result
                    
                    if text and confidence > 0.5:  # Minimum confidence threshold
                        all_text.append(text)
                        total_confidence += confidence
            
            if not all_text:
                return None
            
            # Calculate combined result
            combined_text = " ".join(all_text)
            avg_confidence = total_confidence / len(all_text)
            
            # Post-process text
            cleaned_text = self.postprocess_text(combined_text)
            
            processing_time = time.time() - start_time
            
            return OCRResult(
                text=cleaned_text,
                confidence=avg_confidence,
                bbox=None,  # EasyOCR bbox handling would need more processing
                language="auto",
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"EasyOCR recognition error: {e}")
            return None
    
    def detect_and_recognize(self, image: np.ndarray) -> List[TextDetection]:
        """
        Detect and recognize all text in image using EasyOCR.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of text detections
        """
        if not self._initialized or self._reader is None:
            return []
        
        try:
            # Run OCR
            results = self._reader.readtext(image)
            
            detections = []
            
            for result in results:
                if len(result) >= 3:
                    bbox, text, confidence = result
                    
                    if text and confidence > 0.3:  # Lower threshold for individual detections
                        # Convert bbox to (x, y, width, height)
                        points = np.array(bbox)
                        x_min, y_min = np.min(points, axis=0).astype(int)
                        x_max, y_max = np.max(points, axis=0).astype(int)
                        
                        detection_bbox = (x_min, y_min, x_max - x_min, y_max - y_min)
                        
                        # Post-process text
                        cleaned_text = self.postprocess_text(text)
                        
                        detection = TextDetection(
                            text=cleaned_text,
                            confidence=confidence,
                            bbox=detection_bbox,
                            language="auto"
                        )
                        detections.append(detection)
            
            return detections
            
        except Exception as e:
            self.logger.error(f"EasyOCR detection error: {e}")
            return []
    
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported languages for EasyOCR.
        
        Returns:
            List of language codes
        """
        return [
            "en", "ja", "ko", "ch_sim", "ch_tra", "th", "vi", "ar", "hi",
            "ru", "de", "fr", "es", "pt", "it", "nl", "pl", "tr", "sv"
        ]
    
    def cleanup(self) -> None:
        """Clean up EasyOCR resources."""
        if self._reader:
            try:
                # EasyOCR doesn't have explicit cleanup
                self._reader = None
                self.logger.info("EasyOCR engine cleaned up")
            except Exception as e:
                self.logger.error(f"Error during EasyOCR cleanup: {e}")
        
        self._initialized = False
