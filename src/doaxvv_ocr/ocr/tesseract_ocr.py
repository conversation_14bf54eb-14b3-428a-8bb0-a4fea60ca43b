"""
Tesseract OCR engine implementation for DOAXVV text recognition.

This module provides integration with Tesseract OCR as a reliable
fallback engine for text recognition.
"""

import time
from typing import List, Optional, Dict, Any

import numpy as np
import cv2

from .base_ocr import BaseOCREngine, OCRResult, TextDetection


class TesseractOCREngine(BaseOCREngine):
    """Tesseract OCR engine implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Tesseract OCR engine.
        
        Args:
            config: Tesseract configuration
        """
        super().__init__(config)
        
        # Default configuration
        self._default_config = {
            "lang": "eng+jpn",
            "config": "--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz "
        }
        
        # Merge with provided config
        self._tesseract_config = {**self._default_config, **config}
    
    def initialize(self) -> bool:
        """
        Initialize Tesseract OCR engine.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            import pytesseract
            
            # Test Tesseract installation
            version = pytesseract.get_tesseract_version()
            self.logger.info(f"Tesseract version: {version}")
            
            # Test with a small dummy image
            test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
            cv2.putText(test_image, "TEST", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            
            _ = pytesseract.image_to_string(test_image, lang=self._tesseract_config["lang"])
            
            self._initialized = True
            self.logger.info("Tesseract OCR engine initialized successfully")
            return True
            
        except ImportError:
            self.logger.error("pytesseract not installed. Install with: pip install pytesseract")
            return False
        except Exception as e:
            self.logger.error(f"Failed to initialize Tesseract: {e}")
            return False
    
    def recognize(self, image: np.ndarray) -> Optional[OCRResult]:
        """
        Recognize text in image using Tesseract.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            OCR result or None if recognition failed
        """
        if not self._initialized:
            return None
        
        try:
            import pytesseract
            
            start_time = time.time()
            
            # Preprocess image for Tesseract
            processed_image = self.preprocess_image(image)
            
            # Run OCR
            text = pytesseract.image_to_string(
                processed_image,
                lang=self._tesseract_config["lang"],
                config=self._tesseract_config["config"]
            )
            
            if not text or not text.strip():
                return None
            
            # Get confidence data
            try:
                data = pytesseract.image_to_data(
                    processed_image,
                    lang=self._tesseract_config["lang"],
                    config=self._tesseract_config["config"],
                    output_type=pytesseract.Output.DICT
                )
                
                # Calculate average confidence
                confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                avg_confidence = sum(confidences) / len(confidences) / 100.0 if confidences else 0.5
                
            except Exception:
                avg_confidence = 0.5  # Default confidence
            
            # Post-process text
            cleaned_text = self.postprocess_text(text)
            
            if not cleaned_text:
                return None
            
            processing_time = time.time() - start_time
            
            return OCRResult(
                text=cleaned_text,
                confidence=avg_confidence,
                bbox=None,
                language=self._tesseract_config["lang"],
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Tesseract recognition error: {e}")
            return None
    
    def detect_and_recognize(self, image: np.ndarray) -> List[TextDetection]:
        """
        Detect and recognize all text in image using Tesseract.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of text detections
        """
        if not self._initialized:
            return []
        
        try:
            import pytesseract
            
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            # Get detailed data from Tesseract
            data = pytesseract.image_to_data(
                processed_image,
                lang=self._tesseract_config["lang"],
                config=self._tesseract_config["config"],
                output_type=pytesseract.Output.DICT
            )
            
            detections = []
            
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i]) / 100.0
                
                if text and confidence > 0.3:  # Minimum confidence threshold
                    x = data['left'][i]
                    y = data['top'][i]
                    w = data['width'][i]
                    h = data['height'][i]
                    
                    # Post-process text
                    cleaned_text = self.postprocess_text(text)
                    
                    if cleaned_text:
                        detection = TextDetection(
                            text=cleaned_text,
                            confidence=confidence,
                            bbox=(x, y, w, h),
                            language=self._tesseract_config["lang"]
                        )
                        detections.append(detection)
            
            return detections
            
        except Exception as e:
            self.logger.error(f"Tesseract detection error: {e}")
            return []
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for better Tesseract results.
        
        Args:
            image: Input image
            
        Returns:
            Preprocessed image
        """
        # Convert to grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # Apply threshold to get binary image
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Resize if image is too small
        height, width = binary.shape
        if height < 32 or width < 32:
            scale_factor = max(32 / height, 32 / width)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            binary = cv2.resize(binary, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        return binary
    
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported languages for Tesseract.
        
        Returns:
            List of language codes
        """
        return [
            "eng", "jpn", "chi_sim", "chi_tra", "kor", "ara", "deu", "fra",
            "spa", "por", "ita", "rus", "hin", "tha", "vie"
        ]
    
    def set_language(self, language: str) -> bool:
        """
        Set recognition language for Tesseract.
        
        Args:
            language: Language code
            
        Returns:
            True if successful, False otherwise
        """
        if language not in self.get_supported_languages():
            return False
        
        self._tesseract_config["lang"] = language
        self.logger.info(f"Tesseract language set to: {language}")
        return True
    
    def cleanup(self) -> None:
        """Clean up Tesseract resources."""
        # Tesseract doesn't require explicit cleanup
        self._initialized = False
        self.logger.info("Tesseract OCR engine cleaned up")
