"""
OCR Manager for coordinating multiple OCR engines.

This module manages multiple OCR engines and provides intelligent
fallback mechanisms for optimal text recognition performance.
"""

import time
from typing import List, Optional, Dict, Any
import logging

import numpy as np

from .base_ocr import BaseOCREngine, OCRResult, TextDetection
from .paddle_ocr import PaddleOCREngine
from .easy_ocr import EasyOCREngine
from .tesseract_ocr import TesseractOCREngine
from ..config.settings import OCRConfig


class OCRManager:
    """
    Manager for coordinating multiple OCR engines with intelligent fallback.
    
    This class manages multiple OCR engines and automatically selects the best
    engine based on performance and accuracy requirements.
    """
    
    def __init__(self, config: OCRConfig):
        """
        Initialize OCR manager.
        
        Args:
            config: OCR configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize engines
        self.engines: Dict[str, BaseOCREngine] = {}
        self._engine_performance: Dict[str, Dict[str, float]] = {}
        self._initialization_order = [
            config.primary_engine,
            config.secondary_engine,
            config.fallback_engine
        ]
        
        # Remove duplicates while preserving order
        seen = set()
        self._initialization_order = [
            x for x in self._initialization_order 
            if not (x in seen or seen.add(x))
        ]
        
        self._initialize_engines()
    
    def _initialize_engines(self) -> None:
        """Initialize all configured OCR engines."""
        engine_configs = {
            "paddleocr": self.config.paddleocr_settings,
            "easyocr": self.config.easyocr_settings,
            "tesseract": self.config.tesseract_settings
        }
        
        engine_classes = {
            "paddleocr": PaddleOCREngine,
            "easyocr": EasyOCREngine,
            "tesseract": TesseractOCREngine
        }
        
        for engine_name in self._initialization_order:
            if engine_name in engine_classes:
                try:
                    engine_config = engine_configs.get(engine_name, {})
                    engine = engine_classes[engine_name](engine_config)
                    
                    if engine.initialize():
                        self.engines[engine_name] = engine
                        self._engine_performance[engine_name] = {
                            "avg_time": 0.0,
                            "success_rate": 1.0,
                            "total_calls": 0,
                            "successful_calls": 0
                        }
                        self.logger.info(f"Initialized {engine_name} engine")
                    else:
                        self.logger.warning(f"Failed to initialize {engine_name} engine")
                        
                except Exception as e:
                    self.logger.error(f"Error initializing {engine_name} engine: {e}")
        
        if not self.engines:
            raise RuntimeError("No OCR engines could be initialized")
        
        self.logger.info(f"OCR Manager initialized with {len(self.engines)} engines")
    
    def recognize(self, image: np.ndarray, prefer_speed: bool = False) -> Optional[OCRResult]:
        """
        Recognize text in image using the best available engine.
        
        Args:
            image: Input image as numpy array
            prefer_speed: If True, prefer faster engines over accuracy
            
        Returns:
            OCR result or None if all engines failed
        """
        if not self.engines:
            return None
        
        # Select engine order based on preference
        engine_order = self._get_engine_order(prefer_speed)
        
        for engine_name in engine_order:
            engine = self.engines.get(engine_name)
            if not engine:
                continue
            
            try:
                start_time = time.time()
                result = engine.recognize(image)
                processing_time = time.time() - start_time
                
                # Update performance metrics
                self._update_performance_metrics(engine_name, processing_time, result is not None)
                
                if result and result.confidence > 0.5:  # Minimum confidence threshold
                    result.processing_time = processing_time
                    self.logger.debug(f"Successful recognition with {engine_name}: {result.text[:50]}...")
                    return result
                    
            except Exception as e:
                self.logger.error(f"Error in {engine_name} recognition: {e}")
                self._update_performance_metrics(engine_name, 0.0, False)
        
        self.logger.warning("All OCR engines failed to recognize text")
        return None
    
    def detect_and_recognize(self, image: np.ndarray) -> List[TextDetection]:
        """
        Detect and recognize all text in image.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of text detections
        """
        if not self.engines:
            return []
        
        # Use primary engine for detection
        primary_engine_name = self.config.primary_engine
        engine = self.engines.get(primary_engine_name)
        
        if not engine:
            # Fallback to first available engine
            engine_name = next(iter(self.engines.keys()))
            engine = self.engines[engine_name]
        
        try:
            start_time = time.time()
            detections = engine.detect_and_recognize(image)
            processing_time = time.time() - start_time
            
            self._update_performance_metrics(primary_engine_name, processing_time, len(detections) > 0)
            
            self.logger.debug(f"Detected {len(detections)} text regions")
            return detections
            
        except Exception as e:
            self.logger.error(f"Error in text detection: {e}")
            return []
    
    def _get_engine_order(self, prefer_speed: bool = False) -> List[str]:
        """
        Get engine order based on performance and preference.
        
        Args:
            prefer_speed: If True, prioritize faster engines
            
        Returns:
            List of engine names in order of preference
        """
        if prefer_speed:
            # Sort by average processing time (ascending)
            return sorted(
                self.engines.keys(),
                key=lambda name: self._engine_performance[name]["avg_time"]
            )
        else:
            # Sort by success rate (descending), then by speed
            return sorted(
                self.engines.keys(),
                key=lambda name: (
                    -self._engine_performance[name]["success_rate"],
                    self._engine_performance[name]["avg_time"]
                )
            )
    
    def _update_performance_metrics(self, engine_name: str, processing_time: float, success: bool) -> None:
        """
        Update performance metrics for an engine.
        
        Args:
            engine_name: Name of the engine
            processing_time: Time taken for processing
            success: Whether the operation was successful
        """
        if engine_name not in self._engine_performance:
            return
        
        metrics = self._engine_performance[engine_name]
        metrics["total_calls"] += 1
        
        if success:
            metrics["successful_calls"] += 1
        
        # Update success rate
        metrics["success_rate"] = metrics["successful_calls"] / metrics["total_calls"]
        
        # Update average processing time (exponential moving average)
        if metrics["avg_time"] == 0.0:
            metrics["avg_time"] = processing_time
        else:
            alpha = 0.1  # Smoothing factor
            metrics["avg_time"] = alpha * processing_time + (1 - alpha) * metrics["avg_time"]
    
    def get_performance_metrics(self) -> Dict[str, Dict[str, float]]:
        """
        Get performance metrics for all engines.
        
        Returns:
            Dictionary of engine performance metrics
        """
        return self._engine_performance.copy()
    
    def get_available_engines(self) -> List[str]:
        """
        Get list of available engine names.
        
        Returns:
            List of engine names
        """
        return list(self.engines.keys())
    
    def is_engine_available(self, engine_name: str) -> bool:
        """
        Check if a specific engine is available.
        
        Args:
            engine_name: Name of the engine
            
        Returns:
            True if engine is available, False otherwise
        """
        return engine_name in self.engines
    
    def cleanup(self) -> None:
        """Clean up all engines."""
        for engine_name, engine in self.engines.items():
            try:
                engine.cleanup()
                self.logger.info(f"Cleaned up {engine_name} engine")
            except Exception as e:
                self.logger.error(f"Error cleaning up {engine_name} engine: {e}")
        
        self.engines.clear()
        self._engine_performance.clear()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
