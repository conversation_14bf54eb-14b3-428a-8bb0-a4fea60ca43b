"""
Base OCR engine interface for DOAXVV text recognition system.

This module provides the abstract base class that all OCR engine
implementations must inherit from.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Tuple, Dict, Any
from dataclasses import dataclass
import logging

import numpy as np


@dataclass
class OCRResult:
    """Result of OCR operation."""
    text: str
    confidence: float
    bbox: Optional[Tuple[int, int, int, int]] = None  # (x, y, width, height)
    language: Optional[str] = None
    processing_time: Optional[float] = None


@dataclass
class TextDetection:
    """Individual text detection result."""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x, y, width, height)
    language: Optional[str] = None


class BaseOCREngine(ABC):
    """Abstract base class for OCR engines."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize OCR engine.
        
        Args:
            config: Engine-specific configuration
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self._initialized = False
        self._engine = None
        
    @abstractmethod
    def initialize(self) -> bool:
        """
        Initialize the OCR engine.
        
        Returns:
            True if initialization successful, False otherwise
        """
        pass
    
    @abstractmethod
    def recognize(self, image: np.ndarray) -> Optional[OCRResult]:
        """
        Recognize text in image.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            OCR result or None if recognition failed
        """
        pass
    
    @abstractmethod
    def detect_and_recognize(self, image: np.ndarray) -> List[TextDetection]:
        """
        Detect and recognize all text in image.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of text detections
        """
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """Clean up engine resources."""
        pass
    
    def is_initialized(self) -> bool:
        """Check if engine is initialized."""
        return self._initialized
    
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported languages.
        
        Returns:
            List of language codes
        """
        return ["en", "ja"]  # Default supported languages
    
    def set_language(self, language: str) -> bool:
        """
        Set recognition language.
        
        Args:
            language: Language code (e.g., 'en', 'ja')
            
        Returns:
            True if successful, False otherwise
        """
        # Default implementation - override in subclasses
        return language in self.get_supported_languages()
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for better OCR results.
        
        Args:
            image: Input image
            
        Returns:
            Preprocessed image
        """
        # Default implementation - can be overridden
        return image
    
    def postprocess_text(self, text: str) -> str:
        """
        Postprocess recognized text.
        
        Args:
            text: Raw recognized text
            
        Returns:
            Cleaned text
        """
        # Default implementation - basic cleaning
        if not text:
            return ""
        
        # Remove extra whitespace
        text = " ".join(text.split())
        
        # Remove common OCR artifacts
        text = text.replace("'", "'").replace(""", '"').replace(""", '"')
        
        return text.strip()
    
    def __enter__(self):
        """Context manager entry."""
        if not self.initialize():
            raise RuntimeError(f"Failed to initialize {self.__class__.__name__}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
