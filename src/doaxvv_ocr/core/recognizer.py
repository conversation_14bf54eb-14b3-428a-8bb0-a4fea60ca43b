"""
Main text recognizer class for DOAXVV.

This module provides the primary interface for real-time text recognition
specifically optimized for Dead or Alive Xtreme Venus Vacation.
"""

import time
import threading
from typing import Dict, List, Optional, Tuple, Callable
from dataclasses import dataclass
import logging

import cv2
import numpy as np

from ..config.settings import Config
from ..capture.screen_capture import ScreenCaptureFactory
from ..ocr.ocr_manager import OCRManager
from .pipeline import TextDetectionPipeline


@dataclass
class RecognitionResult:
    """Result of text recognition operation."""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x, y, width, height)
    language: str
    processing_time: float
    timestamp: float


@dataclass
class PerformanceMetrics:
    """Performance metrics for monitoring system performance."""
    fps: float
    avg_processing_time: float
    cpu_usage: float
    memory_usage: float
    frame_count: int
    total_time: float


class DOAXVVTextRecognizer:
    """
    Main text recognizer for DOAXVV with real-time capabilities.
    
    This class provides high-performance text recognition specifically
    optimized for DOAXVV's UI elements, fonts, and visual styles.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the DOAXVV text recognizer.
        
        Args:
            config_path: Path to configuration file. If None, uses default config.
        """
        self.config = Config(config_path)
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.screen_capture = ScreenCaptureFactory.create(self.config.capture)
        self.ocr_manager = OCRManager(self.config.ocr)
        self.pipeline = TextDetectionPipeline(self.config.pipeline)
        
        # Runtime state
        self._running = False
        self._thread = None
        self._frame_count = 0
        self._start_time = None
        self._last_frame_time = 0
        
        # Performance tracking
        self._processing_times = []
        self._fps_counter = 0
        self._fps_start_time = 0
        
        # Callbacks
        self._text_callback: Optional[Callable[[List[RecognitionResult]], None]] = None
        self._performance_callback: Optional[Callable[[PerformanceMetrics], None]] = None
        
        self.logger.info("DOAXVV Text Recognizer initialized")
    
    def set_text_callback(self, callback: Callable[[List[RecognitionResult]], None]):
        """Set callback function for text recognition results."""
        self._text_callback = callback
    
    def set_performance_callback(self, callback: Callable[[PerformanceMetrics], None]):
        """Set callback function for performance metrics."""
        self._performance_callback = callback
    
    def start_realtime_recognition(self) -> None:
        """Start real-time text recognition in a separate thread."""
        if self._running:
            self.logger.warning("Recognition already running")
            return
        
        self._running = True
        self._start_time = time.time()
        self._fps_start_time = time.time()
        self._frame_count = 0
        self._fps_counter = 0
        
        self._thread = threading.Thread(target=self._recognition_loop, daemon=True)
        self._thread.start()
        
        self.logger.info("Started real-time text recognition")
    
    def stop_realtime_recognition(self) -> None:
        """Stop real-time text recognition."""
        if not self._running:
            return
        
        self._running = False
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5.0)
        
        self.logger.info("Stopped real-time text recognition")
    
    def recognize_frame(self, frame: np.ndarray) -> List[RecognitionResult]:
        """
        Recognize text in a single frame.
        
        Args:
            frame: Input image frame as numpy array
            
        Returns:
            List of recognition results
        """
        start_time = time.time()
        
        try:
            # Process frame through pipeline
            processed_regions = self.pipeline.process_frame(frame)
            
            # Perform OCR on each region
            results = []
            for region_data in processed_regions:
                region_image = region_data['image']
                bbox = region_data['bbox']
                
                # Run OCR
                ocr_result = self.ocr_manager.recognize(region_image)
                
                if ocr_result and ocr_result.confidence > self.config.pipeline.min_confidence:
                    result = RecognitionResult(
                        text=ocr_result.text,
                        confidence=ocr_result.confidence,
                        bbox=bbox,
                        language=ocr_result.language,
                        processing_time=time.time() - start_time,
                        timestamp=time.time()
                    )
                    results.append(result)
            
            # Track performance
            processing_time = time.time() - start_time
            self._processing_times.append(processing_time)
            
            # Keep only recent processing times for averaging
            if len(self._processing_times) > 100:
                self._processing_times = self._processing_times[-100:]
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in frame recognition: {e}")
            return []
    
    def _recognition_loop(self) -> None:
        """Main recognition loop running in separate thread."""
        self.logger.info("Recognition loop started")
        
        try:
            while self._running:
                loop_start = time.time()
                
                # Capture frame
                frame = self.screen_capture.capture()
                if frame is None:
                    time.sleep(0.01)  # Brief pause if capture failed
                    continue
                
                # Recognize text
                results = self.recognize_frame(frame)
                
                # Call text callback if set
                if self._text_callback and results:
                    try:
                        self._text_callback(results)
                    except Exception as e:
                        self.logger.error(f"Error in text callback: {e}")
                
                # Update performance metrics
                self._frame_count += 1
                self._fps_counter += 1
                
                # Calculate and report performance metrics every second
                current_time = time.time()
                if current_time - self._fps_start_time >= 1.0:
                    metrics = self._calculate_performance_metrics()
                    
                    if self._performance_callback:
                        try:
                            self._performance_callback(metrics)
                        except Exception as e:
                            self.logger.error(f"Error in performance callback: {e}")
                    
                    self._fps_counter = 0
                    self._fps_start_time = current_time
                
                # Frame rate limiting
                loop_time = time.time() - loop_start
                target_frame_time = 1.0 / self.config.performance.target_fps
                
                if loop_time < target_frame_time:
                    time.sleep(target_frame_time - loop_time)
                
        except Exception as e:
            self.logger.error(f"Error in recognition loop: {e}")
        finally:
            self.logger.info("Recognition loop ended")
    
    def _calculate_performance_metrics(self) -> PerformanceMetrics:
        """Calculate current performance metrics."""
        current_time = time.time()
        total_time = current_time - self._start_time if self._start_time else 0
        
        # Calculate FPS
        fps = self._fps_counter / (current_time - self._fps_start_time) if self._fps_start_time else 0
        
        # Calculate average processing time
        avg_processing_time = (
            sum(self._processing_times) / len(self._processing_times) 
            if self._processing_times else 0
        )
        
        # Get system metrics (placeholder - implement with psutil)
        cpu_usage = 0.0  # TODO: Implement with psutil
        memory_usage = 0.0  # TODO: Implement with psutil
        
        return PerformanceMetrics(
            fps=fps,
            avg_processing_time=avg_processing_time,
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            frame_count=self._frame_count,
            total_time=total_time
        )
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics."""
        return self._calculate_performance_metrics()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_realtime_recognition()
