"""
Text detection pipeline for DOAXVV text recognition system.

This module implements the complete text detection pipeline including
preprocessing, region detection, and post-processing stages.
"""

import time
from typing import List, Dict, Any, Tuple, Optional
import logging

import cv2
import numpy as np

from ..config.settings import PipelineConfig
from ..preprocessing.image_processor import ImageProcessor
from ..postprocessing.text_processor import TextProcessor


class TextDetectionPipeline:
    """
    Complete text detection pipeline for DOAXVV.
    
    This class implements the full pipeline from raw screen capture
    to processed text regions ready for OCR.
    """
    
    def __init__(self, config: PipelineConfig):
        """
        Initialize text detection pipeline.
        
        Args:
            config: Pipeline configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize processors
        self.image_processor = ImageProcessor(config)
        self.text_processor = TextProcessor(config)
        
        # Performance tracking
        self._processing_times = {
            "preprocessing": [],
            "roi_detection": [],
            "total": []
        }
    
    def process_frame(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """
        Process a frame through the complete pipeline.
        
        Args:
            frame: Input frame as numpy array
            
        Returns:
            List of processed regions with metadata
        """
        start_time = time.time()
        
        try:
            # Step 1: Preprocessing
            preprocess_start = time.time()
            processed_frame = self.image_processor.preprocess(frame)
            preprocess_time = time.time() - preprocess_start
            
            # Step 2: Region of Interest detection
            roi_start = time.time()
            if self.config.enable_roi_detection:
                regions = self._detect_roi_regions(processed_frame, frame)
            else:
                # Use full frame as single region
                regions = [{
                    "image": processed_frame,
                    "bbox": (0, 0, frame.shape[1], frame.shape[0]),
                    "type": "full_frame",
                    "confidence": 1.0
                }]
            roi_time = time.time() - roi_start
            
            # Step 3: Filter and validate regions
            valid_regions = self._filter_regions(regions)
            
            total_time = time.time() - start_time
            
            # Update performance metrics
            self._update_performance_metrics(preprocess_time, roi_time, total_time)
            
            self.logger.debug(f"Pipeline processed {len(valid_regions)} regions in {total_time:.3f}s")
            
            return valid_regions
            
        except Exception as e:
            self.logger.error(f"Error in pipeline processing: {e}")
            return []
    
    def _detect_roi_regions(self, processed_frame: np.ndarray, original_frame: np.ndarray) -> List[Dict[str, Any]]:
        """
        Detect regions of interest in the frame.
        
        Args:
            processed_frame: Preprocessed frame
            original_frame: Original frame
            
        Returns:
            List of detected regions
        """
        regions = []
        
        # Extract predefined ROI regions
        for region_name, region_config in self.config.roi_regions.items():
            try:
                x = region_config["x"]
                y = region_config["y"]
                width = region_config["width"]
                height = region_config["height"]
                
                # Ensure region is within frame bounds
                frame_height, frame_width = processed_frame.shape[:2]
                x = max(0, min(x, frame_width - 1))
                y = max(0, min(y, frame_height - 1))
                width = min(width, frame_width - x)
                height = min(height, frame_height - y)
                
                if width > 0 and height > 0:
                    # Extract region
                    region_image = processed_frame[y:y+height, x:x+width]
                    
                    # Check if region contains potential text
                    text_confidence = self._estimate_text_confidence(region_image)
                    
                    if text_confidence > 0.3:  # Minimum confidence for text presence
                        regions.append({
                            "image": region_image,
                            "bbox": (x, y, width, height),
                            "type": region_name,
                            "confidence": text_confidence
                        })
                        
            except Exception as e:
                self.logger.warning(f"Error processing ROI region {region_name}: {e}")
        
        # If no ROI regions found, use adaptive region detection
        if not regions:
            regions = self._adaptive_region_detection(processed_frame)
        
        return regions
    
    def _adaptive_region_detection(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """
        Adaptive region detection when no predefined ROIs contain text.
        
        Args:
            frame: Preprocessed frame
            
        Returns:
            List of detected regions
        """
        regions = []
        
        try:
            # Convert to grayscale for analysis
            if len(frame.shape) == 3:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = frame
            
            # Apply text detection preprocessing
            # Enhance contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            # Detect text-like regions using morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            
            # Apply gradient to highlight text edges
            grad_x = cv2.Sobel(enhanced, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(enhanced, cv2.CV_64F, 0, 1, ksize=3)
            gradient = np.sqrt(grad_x**2 + grad_y**2)
            gradient = np.uint8(gradient / gradient.max() * 255)
            
            # Apply threshold
            _, thresh = cv2.threshold(gradient, 50, 255, cv2.THRESH_BINARY)
            
            # Morphological operations to connect text regions
            morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel, iterations=2)
            morph = cv2.morphologyEx(morph, cv2.MORPH_OPEN, kernel, iterations=1)
            
            # Find contours
            contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter and create regions from contours
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                
                # Filter by size
                if w >= 20 and h >= 10 and w <= frame.shape[1] * 0.8 and h <= frame.shape[0] * 0.8:
                    # Extract region
                    region_image = frame[y:y+h, x:x+w]
                    
                    # Estimate text confidence
                    text_confidence = self._estimate_text_confidence(region_image)
                    
                    if text_confidence > 0.4:
                        regions.append({
                            "image": region_image,
                            "bbox": (x, y, w, h),
                            "type": "adaptive",
                            "confidence": text_confidence
                        })
            
            # Sort by confidence
            regions.sort(key=lambda r: r["confidence"], reverse=True)
            
            # Limit number of regions to prevent performance issues
            regions = regions[:10]
            
        except Exception as e:
            self.logger.error(f"Error in adaptive region detection: {e}")
            # Fallback to full frame
            regions = [{
                "image": frame,
                "bbox": (0, 0, frame.shape[1], frame.shape[0]),
                "type": "fallback",
                "confidence": 0.5
            }]
        
        return regions
    
    def _estimate_text_confidence(self, region: np.ndarray) -> float:
        """
        Estimate the confidence that a region contains text.
        
        Args:
            region: Image region to analyze
            
        Returns:
            Confidence score (0.0 to 1.0)
        """
        try:
            if region.size == 0:
                return 0.0
            
            # Convert to grayscale if needed
            if len(region.shape) == 3:
                gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            else:
                gray = region
            
            # Calculate various text-like features
            features = []
            
            # 1. Edge density
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            features.append(min(edge_density * 10, 1.0))
            
            # 2. Contrast
            contrast = gray.std() / 255.0
            features.append(min(contrast * 2, 1.0))
            
            # 3. Horizontal line density (text typically has horizontal structure)
            kernel_h = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 1))
            horizontal = cv2.morphologyEx(edges, cv2.MORPH_OPEN, kernel_h)
            h_density = np.sum(horizontal > 0) / horizontal.size
            features.append(min(h_density * 20, 1.0))
            
            # 4. Aspect ratio (text regions often have certain aspect ratios)
            height, width = gray.shape
            aspect_ratio = width / height if height > 0 else 0
            if 1.5 <= aspect_ratio <= 10:  # Typical text aspect ratios
                features.append(0.8)
            else:
                features.append(0.3)
            
            # Combine features
            confidence = np.mean(features)
            
            return float(confidence)
            
        except Exception as e:
            self.logger.warning(f"Error estimating text confidence: {e}")
            return 0.5
    
    def _filter_regions(self, regions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter and validate detected regions.
        
        Args:
            regions: List of detected regions
            
        Returns:
            List of valid regions
        """
        valid_regions = []
        
        for region in regions:
            try:
                # Check minimum size
                bbox = region["bbox"]
                width, height = bbox[2], bbox[3]
                
                if width < 10 or height < 5:
                    continue
                
                # Check confidence threshold
                if region["confidence"] < 0.3:
                    continue
                
                # Check image validity
                region_image = region["image"]
                if region_image is None or region_image.size == 0:
                    continue
                
                valid_regions.append(region)
                
            except Exception as e:
                self.logger.warning(f"Error validating region: {e}")
        
        return valid_regions
    
    def _update_performance_metrics(self, preprocess_time: float, roi_time: float, total_time: float) -> None:
        """Update performance tracking metrics."""
        self._processing_times["preprocessing"].append(preprocess_time)
        self._processing_times["roi_detection"].append(roi_time)
        self._processing_times["total"].append(total_time)
        
        # Keep only recent measurements
        for key in self._processing_times:
            if len(self._processing_times[key]) > 100:
                self._processing_times[key] = self._processing_times[key][-100:]
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """
        Get pipeline performance metrics.
        
        Returns:
            Dictionary of performance metrics
        """
        metrics = {}
        
        for stage, times in self._processing_times.items():
            if times:
                metrics[f"{stage}_avg_time"] = np.mean(times)
                metrics[f"{stage}_max_time"] = np.max(times)
                metrics[f"{stage}_min_time"] = np.min(times)
            else:
                metrics[f"{stage}_avg_time"] = 0.0
                metrics[f"{stage}_max_time"] = 0.0
                metrics[f"{stage}_min_time"] = 0.0
        
        return metrics
