"""
X11-based screen capture implementation for Linux.

This module provides screen capture using X11 protocol.
Currently a stub implementation that falls back to MSS.
"""

from .mss_capture import MSSCapture


class X11Capture(MSSCapture):
    """X11 screen capture implementation (currently uses MSS as fallback)."""
    
    def __init__(self, config):
        """Initialize X11 capture (falls back to MSS)."""
        super().__init__(config)
        self.logger.info("X11Capture initialized (using MSS fallback)")
