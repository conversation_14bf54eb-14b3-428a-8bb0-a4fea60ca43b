"""
MSS (Multi-Screen Shot) based screen capture implementation.

This module provides cross-platform screen capture using the MSS library,
which offers good performance and compatibility across Windows, macOS, and Linux.
"""

from typing import Optional, Tuple
import time

import numpy as np
import cv2
import mss

from .screen_capture import BaseScreenCapture
from ..config.settings import CaptureConfig


class MSSCapture(BaseScreenCapture):
    """Screen capture implementation using MSS library."""
    
    def __init__(self, config: CaptureConfig):
        """
        Initialize MSS screen capture.
        
        Args:
            config: Capture configuration
        """
        super().__init__(config)
        self.sct = None
        self.monitor = None
        self._last_capture_time = 0
        self._frame_interval = 1.0 / config.fps_limit if config.fps_limit > 0 else 0
        
    def initialize(self) -> bool:
        """
        Initialize MSS screen capture.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.sct = mss.mss()
            
            # Get monitor information
            monitors = self.sct.monitors
            if self.config.monitor >= len(monitors):
                self.logger.warning(f"Monitor {self.config.monitor} not found, using primary monitor")
                self.config.monitor = 0
            
            # Monitor 0 is all monitors combined, 1+ are individual monitors
            if self.config.monitor == 0:
                self.monitor = monitors[0]  # All monitors
            else:
                self.monitor = monitors[self.config.monitor]
            
            # Apply region if specified
            if self.config.region:
                region = self.config.region
                self.monitor = {
                    "left": self.monitor["left"] + region["x"],
                    "top": self.monitor["top"] + region["y"],
                    "width": region["width"],
                    "height": region["height"]
                }
            
            self._initialized = True
            self.logger.info(f"MSS capture initialized for monitor {self.config.monitor}")
            self.logger.info(f"Capture region: {self.monitor}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize MSS capture: {e}")
            return False
    
    def capture(self) -> Optional[np.ndarray]:
        """
        Capture screenshot using MSS.
        
        Returns:
            Screenshot as numpy array (BGR format) or None if capture failed
        """
        if not self._initialized or not self.sct:
            return None
        
        # Frame rate limiting
        current_time = time.time()
        if self._frame_interval > 0:
            time_since_last = current_time - self._last_capture_time
            if time_since_last < self._frame_interval:
                time.sleep(self._frame_interval - time_since_last)
        
        try:
            # Capture screenshot
            screenshot = self.sct.grab(self.monitor)
            
            # Convert to numpy array
            img = np.array(screenshot)
            
            # MSS returns BGRA, convert to BGR
            if img.shape[2] == 4:
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            elif img.shape[2] == 3:
                img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
            
            self._last_capture_time = time.time()
            return img
            
        except Exception as e:
            self.logger.error(f"Failed to capture screenshot: {e}")
            return None
    
    def get_screen_size(self) -> Tuple[int, int]:
        """
        Get screen dimensions.
        
        Returns:
            Tuple of (width, height)
        """
        if not self._initialized or not self.monitor:
            return (0, 0)
        
        return (self.monitor["width"], self.monitor["height"])
    
    def capture_region(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """
        Capture a specific region of the screen.
        
        Args:
            x: X coordinate of top-left corner
            y: Y coordinate of top-left corner
            width: Width of region
            height: Height of region
            
        Returns:
            Screenshot region as numpy array or None if capture failed
        """
        if not self._initialized or not self.sct:
            return None
        
        try:
            # Create region monitor definition
            region_monitor = {
                "left": self.monitor["left"] + x,
                "top": self.monitor["top"] + y,
                "width": width,
                "height": height
            }
            
            # Capture region
            screenshot = self.sct.grab(region_monitor)
            
            # Convert to numpy array
            img = np.array(screenshot)
            
            # MSS returns BGRA, convert to BGR
            if img.shape[2] == 4:
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            elif img.shape[2] == 3:
                img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
            
            return img
            
        except Exception as e:
            self.logger.error(f"Failed to capture region: {e}")
            return None
    
    def cleanup(self) -> None:
        """Clean up MSS resources."""
        if self.sct:
            try:
                self.sct.close()
            except Exception as e:
                self.logger.error(f"Error during MSS cleanup: {e}")
            finally:
                self.sct = None
        
        self._initialized = False
        self.logger.info("MSS capture cleaned up")
    
    def get_monitors(self) -> list:
        """
        Get list of available monitors.
        
        Returns:
            List of monitor information dictionaries
        """
        if not self.sct:
            return []
        
        return self.sct.monitors
    
    def set_monitor(self, monitor_index: int) -> bool:
        """
        Change the monitor to capture from.
        
        Args:
            monitor_index: Index of monitor to capture
            
        Returns:
            True if successful, False otherwise
        """
        if not self.sct:
            return False
        
        try:
            monitors = self.sct.monitors
            if monitor_index >= len(monitors):
                return False
            
            self.config.monitor = monitor_index
            if monitor_index == 0:
                self.monitor = monitors[0]  # All monitors
            else:
                self.monitor = monitors[monitor_index]
            
            self.logger.info(f"Switched to monitor {monitor_index}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to switch monitor: {e}")
            return False
