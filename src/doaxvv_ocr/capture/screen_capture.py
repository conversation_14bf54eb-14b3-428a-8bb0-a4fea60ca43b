"""
Base screen capture interface and factory for creating platform-specific implementations.

This module provides the abstract base class for screen capture and a factory
for creating the appropriate implementation based on the current platform.
"""

import sys
import platform
from abc import ABC, abstractmethod
from typing import Optional, Tuple, Dict, Any
import logging

import numpy as np
import cv2

from ..config.settings import CaptureConfig


class BaseScreenCapture(ABC):
    """Abstract base class for screen capture implementations."""
    
    def __init__(self, config: CaptureConfig):
        """
        Initialize screen capture.
        
        Args:
            config: Capture configuration
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self._initialized = False
        
    @abstractmethod
    def initialize(self) -> bool:
        """
        Initialize the screen capture system.
        
        Returns:
            True if initialization successful, False otherwise
        """
        pass
    
    @abstractmethod
    def capture(self) -> Optional[np.ndarray]:
        """
        Capture a screenshot.
        
        Returns:
            Screenshot as numpy array (BGR format) or None if capture failed
        """
        pass
    
    @abstractmethod
    def get_screen_size(self) -> Tuple[int, int]:
        """
        Get screen dimensions.
        
        Returns:
            Tuple of (width, height)
        """
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """Clean up resources."""
        pass
    
    def is_initialized(self) -> bool:
        """Check if capture system is initialized."""
        return self._initialized
    
    def capture_region(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """
        Capture a specific region of the screen.
        
        Args:
            x: X coordinate of top-left corner
            y: Y coordinate of top-left corner
            width: Width of region
            height: Height of region
            
        Returns:
            Screenshot region as numpy array or None if capture failed
        """
        full_screenshot = self.capture()
        if full_screenshot is None:
            return None
        
        # Ensure coordinates are within bounds
        screen_height, screen_width = full_screenshot.shape[:2]
        x = max(0, min(x, screen_width - 1))
        y = max(0, min(y, screen_height - 1))
        width = min(width, screen_width - x)
        height = min(height, screen_height - y)
        
        return full_screenshot[y:y+height, x:x+width]
    
    def __enter__(self):
        """Context manager entry."""
        if not self.initialize():
            raise RuntimeError("Failed to initialize screen capture")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()


class ScreenCaptureFactory:
    """Factory for creating platform-specific screen capture implementations."""
    
    @staticmethod
    def create(config: CaptureConfig) -> BaseScreenCapture:
        """
        Create appropriate screen capture implementation.
        
        Args:
            config: Capture configuration
            
        Returns:
            Screen capture implementation
        """
        method = config.method.lower()
        
        if method == "auto":
            method = ScreenCaptureFactory._detect_best_method()
        
        if method == "mss":
            from .mss_capture import MSSCapture
            return MSSCapture(config)
        elif method == "win32" and sys.platform == "win32":
            from .win32_capture import Win32Capture
            return Win32Capture(config)
        elif method == "quartz" and sys.platform == "darwin":
            from .quartz_capture import QuartzCapture
            return QuartzCapture(config)
        elif method == "x11" and sys.platform.startswith("linux"):
            from .x11_capture import X11Capture
            return X11Capture(config)
        else:
            # Fallback to MSS (cross-platform)
            from .mss_capture import MSSCapture
            return MSSCapture(config)
    
    @staticmethod
    def _detect_best_method() -> str:
        """
        Detect the best screen capture method for current platform.
        
        Returns:
            Method name
        """
        system = platform.system().lower()
        
        if system == "windows":
            # Prefer Win32 for better performance on Windows
            return "win32"
        elif system == "darwin":
            # Use Quartz on macOS
            return "quartz"
        elif system == "linux":
            # Use X11 on Linux
            return "x11"
        else:
            # Fallback to MSS for unknown platforms
            return "mss"
    
    @staticmethod
    def get_available_methods() -> Dict[str, bool]:
        """
        Get available screen capture methods for current platform.
        
        Returns:
            Dictionary mapping method names to availability
        """
        methods = {
            "mss": True,  # Always available
            "win32": sys.platform == "win32",
            "quartz": sys.platform == "darwin",
            "x11": sys.platform.startswith("linux")
        }
        
        return methods
