"""
Text postprocessing for DOAXVV text recognition.

This module provides text cleaning and enhancement functionality
specifically optimized for DOAXVV text patterns.
"""

import re
import unicodedata
from typing import List, Optional
import logging

from ..config.settings import PipelineConfig


class TextProcessor:
    """Text postprocessing for cleaning and enhancing recognized text."""
    
    def __init__(self, config: PipelineConfig):
        """
        Initialize text processor.
        
        Args:
            config: Pipeline configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Common DOAXVV text patterns
        self.doaxvv_patterns = {
            "menu_items": [
                "Home", "Gacha", "Shop", "Event", "Collection", "Profile",
                "Venus Points", "Zack Dollars", "Energy", "Level"
            ],
            "dialog_buttons": [
                "OK", "Cancel", "Yes", "No", "Close", "Confirm", "Back"
            ],
            "status_indicators": [
                "HP", "MP", "EXP", "LV", "ATK", "DEF", "SPD"
            ]
        }
    
    def process_text(self, text: str) -> str:
        """
        Process and clean recognized text.
        
        Args:
            text: Raw recognized text
            
        Returns:
            Cleaned and processed text
        """
        if not text:
            return ""
        
        try:
            # Basic cleaning
            processed = self._basic_cleaning(text)
            
            # Character normalization
            processed = self._normalize_characters(processed)
            
            # DOAXVV-specific processing
            processed = self._doaxvv_specific_processing(processed)
            
            # Final validation
            if self._is_valid_text(processed):
                return processed
            else:
                return ""
                
        except Exception as e:
            self.logger.error(f"Error in text processing: {e}")
            return text
    
    def _basic_cleaning(self, text: str) -> str:
        """Apply basic text cleaning."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common OCR artifacts
        text = text.replace('|', 'I')  # Common misrecognition
        text = text.replace('0', 'O')  # In text contexts
        text = text.replace('5', 'S')  # In text contexts
        
        # Remove special characters that are likely artifacts
        text = re.sub(r'[^\w\s\-\.\,\!\?\:\;]', '', text)
        
        return text.strip()
    
    def _normalize_characters(self, text: str) -> str:
        """Normalize character encoding and representations."""
        # Unicode normalization
        text = unicodedata.normalize('NFKC', text)
        
        # Convert full-width characters to half-width
        text = self._convert_fullwidth_to_halfwidth(text)
        
        # Fix common character substitutions
        replacements = {
            ''': "'",
            ''': "'",
            '"': '"',
            '"': '"',
            '–': '-',
            '—': '-',
            '…': '...'
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text
    
    def _convert_fullwidth_to_halfwidth(self, text: str) -> str:
        """Convert full-width characters to half-width."""
        result = []
        for char in text:
            # Full-width ASCII range
            if '\uff01' <= char <= '\uff5e':
                # Convert to half-width
                result.append(chr(ord(char) - 0xfee0))
            else:
                result.append(char)
        return ''.join(result)
    
    def _doaxvv_specific_processing(self, text: str) -> str:
        """Apply DOAXVV-specific text processing."""
        # Fix common DOAXVV text patterns
        doaxvv_fixes = {
            'Gacha': ['Gacha', 'Gatcha', 'Gasha'],
            'Venus Points': ['Venus Points', 'Venus Pts', 'V Points'],
            'Zack Dollars': ['Zack Dollars', 'Z Dollars', 'Zack $'],
            'Collection': ['Collection', 'Collections', 'Collect'],
        }
        
        for correct, variations in doaxvv_fixes.items():
            for variation in variations:
                if variation.lower() in text.lower():
                    text = re.sub(re.escape(variation), correct, text, flags=re.IGNORECASE)
        
        # Handle numeric values with units
        text = re.sub(r'(\d+)\s*[Pp]ts?', r'\1 Points', text)
        text = re.sub(r'(\d+)\s*[Dd]ollars?', r'\1 Dollars', text)
        
        return text
    
    def _is_valid_text(self, text: str) -> bool:
        """Validate if text meets quality criteria."""
        if not text:
            return False
        
        # Check length constraints
        if len(text) < self.config.min_text_length:
            return False
        
        if len(text) > self.config.max_text_length:
            return False
        
        # Check against filter patterns if configured
        if self.config.filter_patterns:
            for pattern in self.config.filter_patterns:
                if re.match(pattern, text):
                    return True
            return False
        
        # Basic validation - must contain alphanumeric characters
        if not re.search(r'[a-zA-Z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]', text):
            return False
        
        return True
    
    def extract_numbers(self, text: str) -> List[int]:
        """Extract numeric values from text."""
        numbers = re.findall(r'\d+', text)
        return [int(num) for num in numbers]
    
    def is_menu_item(self, text: str) -> bool:
        """Check if text is likely a menu item."""
        text_lower = text.lower()
        for category, items in self.doaxvv_patterns.items():
            for item in items:
                if item.lower() in text_lower:
                    return True
        return False
    
    def get_text_category(self, text: str) -> Optional[str]:
        """Determine the category of recognized text."""
        text_lower = text.lower()
        
        for category, items in self.doaxvv_patterns.items():
            for item in items:
                if item.lower() in text_lower:
                    return category
        
        # Check for numeric patterns
        if re.match(r'^\d+$', text):
            return "number"
        
        if re.search(r'\d+\s*(points?|dollars?|energy|exp)', text_lower):
            return "status_value"
        
        return "general_text"
