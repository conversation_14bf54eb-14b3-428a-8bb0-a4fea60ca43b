#!/usr/bin/env python3
"""
Command-line interface for DOAXVV Text Recognition System.

This module provides a CLI for running the text recognition system
with various options and configurations.
"""

import argparse
import sys
import time
import logging
from pathlib import Path
from typing import List

from .core.recognizer import DOAXVV<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RecognitionResult, PerformanceMetrics
from .config.settings import Config


def setup_logging(verbose: bool = False) -> None:
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def text_callback(results: List[RecognitionResult]) -> None:
    """Callback function for text recognition results."""
    for result in results:
        print(f"[{result.confidence:.2f}] {result.text}")


def performance_callback(metrics: PerformanceMetrics) -> None:
    """Callback function for performance metrics."""
    print(f"FPS: {metrics.fps:.1f} | "
          f"Avg Time: {metrics.avg_processing_time*1000:.1f}ms | "
          f"Frames: {metrics.frame_count}")


def run_realtime(config_path: str = None, duration: int = None) -> None:
    """
    Run real-time text recognition.
    
    Args:
        config_path: Path to configuration file
        duration: Duration to run in seconds (None for indefinite)
    """
    print("Starting DOAXVV Real-Time Text Recognition...")
    
    try:
        # Initialize recognizer
        recognizer = DOAXVVTextRecognizer(config_path)
        
        # Set callbacks
        recognizer.set_text_callback(text_callback)
        recognizer.set_performance_callback(performance_callback)
        
        # Start recognition
        recognizer.start_realtime_recognition()
        
        print("Recognition started. Press Ctrl+C to stop.")
        
        # Run for specified duration or until interrupted
        start_time = time.time()
        try:
            while True:
                time.sleep(1)
                
                if duration and (time.time() - start_time) >= duration:
                    break
                    
        except KeyboardInterrupt:
            print("\nStopping recognition...")
        
        # Stop recognition
        recognizer.stop_realtime_recognition()
        
        # Print final metrics
        final_metrics = recognizer.get_performance_metrics()
        print(f"\nFinal Statistics:")
        print(f"Total Frames: {final_metrics.frame_count}")
        print(f"Average FPS: {final_metrics.fps:.2f}")
        print(f"Total Time: {final_metrics.total_time:.2f}s")
        print(f"Average Processing Time: {final_metrics.avg_processing_time*1000:.2f}ms")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


def test_capture() -> None:
    """Test screen capture functionality."""
    print("Testing screen capture...")
    
    try:
        from .capture.screen_capture import ScreenCaptureFactory
        from .config.settings import CaptureConfig
        
        config = CaptureConfig()
        capture = ScreenCaptureFactory.create(config)
        
        with capture:
            frame = capture.capture()
            if frame is not None:
                print(f"Capture successful! Frame size: {frame.shape}")
                
                # Save test image
                import cv2
                cv2.imwrite("test_capture.png", frame)
                print("Test capture saved as 'test_capture.png'")
            else:
                print("Capture failed!")
                
    except Exception as e:
        print(f"Capture test error: {e}")


def benchmark_ocr() -> None:
    """Benchmark OCR engines."""
    print("Benchmarking OCR engines...")
    
    try:
        from .ocr.ocr_manager import OCRManager
        from .config.settings import OCRConfig
        import numpy as np
        import cv2
        
        # Create test image with text
        test_image = np.ones((100, 400, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "DOAXVV Test Text", (10, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        config = OCRConfig()
        ocr_manager = OCRManager(config)
        
        # Run benchmark
        num_iterations = 10
        total_time = 0
        
        for i in range(num_iterations):
            start_time = time.time()
            result = ocr_manager.recognize(test_image)
            end_time = time.time()
            
            total_time += (end_time - start_time)
            
            if result:
                print(f"Iteration {i+1}: '{result.text}' (confidence: {result.confidence:.2f})")
            else:
                print(f"Iteration {i+1}: No text detected")
        
        avg_time = total_time / num_iterations
        print(f"\nBenchmark Results:")
        print(f"Average processing time: {avg_time*1000:.2f}ms")
        print(f"Estimated FPS: {1/avg_time:.1f}")
        
        # Print engine performance
        performance = ocr_manager.get_performance_metrics()
        for engine, metrics in performance.items():
            print(f"{engine}: {metrics['avg_time']*1000:.2f}ms avg, "
                  f"{metrics['success_rate']*100:.1f}% success rate")
        
        ocr_manager.cleanup()
        
    except Exception as e:
        print(f"Benchmark error: {e}")


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="DOAXVV Real-Time Text Recognition System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s realtime                    # Run real-time recognition
  %(prog)s realtime -d 60              # Run for 60 seconds
  %(prog)s realtime -c config.yaml     # Use custom config
  %(prog)s test-capture                # Test screen capture
  %(prog)s benchmark                   # Benchmark OCR engines
        """
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Real-time recognition command
    realtime_parser = subparsers.add_parser("realtime", help="Run real-time text recognition")
    realtime_parser.add_argument(
        "-c", "--config",
        type=str,
        help="Path to configuration file"
    )
    realtime_parser.add_argument(
        "-d", "--duration",
        type=int,
        help="Duration to run in seconds"
    )
    
    # Test capture command
    subparsers.add_parser("test-capture", help="Test screen capture functionality")
    
    # Benchmark command
    subparsers.add_parser("benchmark", help="Benchmark OCR engines")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Execute command
    if args.command == "realtime":
        run_realtime(args.config, args.duration)
    elif args.command == "test-capture":
        test_capture()
    elif args.command == "benchmark":
        benchmark_ocr()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
