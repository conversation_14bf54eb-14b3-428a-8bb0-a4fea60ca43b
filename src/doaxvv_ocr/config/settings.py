"""
Configuration management for DOAXVV text recognition system.

This module handles loading and managing configuration settings for all
components of the text recognition system.
"""

import os
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class CaptureConfig:
    """Configuration for screen capture."""
    method: str = "auto"  # auto, mss, win32, quartz, x11
    monitor: int = 0  # Monitor index to capture
    region: Optional[Dict[str, int]] = None  # {x, y, width, height}
    fps_limit: int = 30
    buffer_size: int = 3


@dataclass
class OCRConfig:
    """Configuration for OCR engines."""
    primary_engine: str = "paddleocr"
    secondary_engine: str = "easyocr"
    fallback_engine: str = "tesseract"
    
    # Engine-specific settings
    paddleocr_settings: Dict[str, Any] = field(default_factory=lambda: {
        "use_angle_cls": True,
        "lang": "en",
        "use_gpu": True,
        "show_log": False
    })
    
    easyocr_settings: Dict[str, Any] = field(default_factory=lambda: {
        "languages": ["en", "ja"],
        "gpu": True,
        "verbose": False
    })
    
    tesseract_settings: Dict[str, Any] = field(default_factory=lambda: {
        "lang": "eng+jpn",
        "config": "--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz "
    })


@dataclass
class PipelineConfig:
    """Configuration for text detection pipeline."""
    # Preprocessing
    resize_factor: float = 1.0
    gaussian_blur: bool = True
    blur_kernel_size: int = 3
    contrast_enhancement: bool = True
    brightness_adjustment: float = 0.0
    
    # Region detection
    enable_roi_detection: bool = True
    roi_regions: Dict[str, Dict[str, int]] = field(default_factory=lambda: {
        "menu_area": {"x": 0, "y": 0, "width": 400, "height": 100},
        "notification_area": {"x": 0, "y": 0, "width": 800, "height": 200},
        "dialog_area": {"x": 200, "y": 150, "width": 600, "height": 400}
    })
    
    # Text filtering
    min_confidence: float = 0.7
    min_text_length: int = 2
    max_text_length: int = 200
    filter_patterns: list = field(default_factory=list)


@dataclass
class PerformanceConfig:
    """Configuration for performance optimization."""
    target_fps: int = 20
    max_processing_time: float = 0.1  # 100ms
    enable_multithreading: bool = True
    thread_pool_size: int = 4
    enable_gpu_acceleration: bool = True
    memory_limit_mb: int = 2048


@dataclass
class UIConfig:
    """Configuration for user interface."""
    show_overlay: bool = True
    overlay_opacity: float = 0.8
    highlight_color: str = "#00FF00"
    font_size: int = 12
    show_confidence: bool = True
    show_fps: bool = True


class Config:
    """Main configuration class for DOAXVV text recognition system."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration.
        
        Args:
            config_path: Path to configuration file. If None, uses default config.
        """
        self.capture = CaptureConfig()
        self.ocr = OCRConfig()
        self.pipeline = PipelineConfig()
        self.performance = PerformanceConfig()
        self.ui = UIConfig()
        
        # Load configuration from file if provided
        if config_path:
            self.load_from_file(config_path)
        else:
            # Try to load default config
            default_config_path = self._get_default_config_path()
            if default_config_path.exists():
                self.load_from_file(str(default_config_path))
    
    def _get_default_config_path(self) -> Path:
        """Get path to default configuration file."""
        # Look for config in several locations
        possible_paths = [
            Path.cwd() / "config.yaml",
            Path.cwd() / "doaxvv_config.yaml",
            Path(__file__).parent / "default_config.yaml",
            Path.home() / ".doaxvv_ocr" / "config.yaml"
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
        
        # Return first path as default location
        return possible_paths[0]
    
    def load_from_file(self, config_path: str) -> None:
        """
        Load configuration from YAML file.
        
        Args:
            config_path: Path to configuration file
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # Update configuration sections
            if 'capture' in config_data:
                self._update_dataclass(self.capture, config_data['capture'])
            
            if 'ocr' in config_data:
                self._update_dataclass(self.ocr, config_data['ocr'])
            
            if 'pipeline' in config_data:
                self._update_dataclass(self.pipeline, config_data['pipeline'])
            
            if 'performance' in config_data:
                self._update_dataclass(self.performance, config_data['performance'])
            
            if 'ui' in config_data:
                self._update_dataclass(self.ui, config_data['ui'])
                
        except Exception as e:
            raise ValueError(f"Failed to load configuration from {config_path}: {e}")
    
    def save_to_file(self, config_path: str) -> None:
        """
        Save configuration to YAML file.
        
        Args:
            config_path: Path to save configuration file
        """
        config_data = {
            'capture': self._dataclass_to_dict(self.capture),
            'ocr': self._dataclass_to_dict(self.ocr),
            'pipeline': self._dataclass_to_dict(self.pipeline),
            'performance': self._dataclass_to_dict(self.performance),
            'ui': self._dataclass_to_dict(self.ui)
        }
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, indent=2)
    
    def _update_dataclass(self, dataclass_instance, update_dict: Dict[str, Any]) -> None:
        """Update dataclass instance with values from dictionary."""
        for key, value in update_dict.items():
            if hasattr(dataclass_instance, key):
                setattr(dataclass_instance, key, value)
    
    def _dataclass_to_dict(self, dataclass_instance) -> Dict[str, Any]:
        """Convert dataclass instance to dictionary."""
        result = {}
        for field_name in dataclass_instance.__dataclass_fields__:
            value = getattr(dataclass_instance, field_name)
            result[field_name] = value
        return result
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get complete configuration as dictionary."""
        return {
            'capture': self._dataclass_to_dict(self.capture),
            'ocr': self._dataclass_to_dict(self.ocr),
            'pipeline': self._dataclass_to_dict(self.pipeline),
            'performance': self._dataclass_to_dict(self.performance),
            'ui': self._dataclass_to_dict(self.ui)
        }
