# Default configuration for DOAXVV Text Recognition System

# Screen capture configuration
capture:
  method: "auto"  # auto, mss, win32, quartz, x11
  monitor: 0      # Monitor index to capture (0 = primary)
  region: null    # Capture region {x, y, width, height} or null for full screen
  fps_limit: 30   # Maximum capture FPS
  buffer_size: 3  # Frame buffer size

# OCR engine configuration
ocr:
  primary_engine: "paddleocr"    # Primary OCR engine
  secondary_engine: "easyocr"    # Secondary OCR engine
  fallback_engine: "tesseract"   # Fallback OCR engine
  
  # PaddleOCR settings
  paddleocr_settings:
    use_angle_cls: true
    lang: "en"
    use_gpu: true
    show_log: false
    det_model_dir: null
    rec_model_dir: null
    cls_model_dir: null
  
  # EasyOCR settings
  easyocr_settings:
    languages: ["en", "ja"]
    gpu: true
    verbose: false
    detector: true
    recognizer: true
  
  # Tesseract settings
  tesseract_settings:
    lang: "eng+jpn"
    config: "--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef<PERSON><PERSON><PERSON>lmnopqrstuvwxyz "

# Text detection pipeline configuration
pipeline:
  # Preprocessing settings
  resize_factor: 1.0
  gaussian_blur: true
  blur_kernel_size: 3
  contrast_enhancement: true
  brightness_adjustment: 0.0
  
  # Region of Interest (ROI) detection
  enable_roi_detection: true
  roi_regions:
    menu_area:
      x: 0
      y: 0
      width: 400
      height: 100
    notification_area:
      x: 0
      y: 0
      width: 800
      height: 200
    dialog_area:
      x: 200
      y: 150
      width: 600
      height: 400
    status_bar:
      x: 0
      y: 720
      width: 1280
      height: 80
  
  # Text filtering
  min_confidence: 0.7
  min_text_length: 2
  max_text_length: 200
  filter_patterns: []

# Performance optimization configuration
performance:
  target_fps: 20                    # Target processing FPS
  max_processing_time: 0.1          # Maximum processing time per frame (seconds)
  enable_multithreading: true       # Enable multi-threading
  thread_pool_size: 4               # Number of worker threads
  enable_gpu_acceleration: true     # Enable GPU acceleration when available
  memory_limit_mb: 2048            # Memory usage limit in MB

# User interface configuration
ui:
  show_overlay: true          # Show text overlay on screen
  overlay_opacity: 0.8        # Overlay transparency (0.0 - 1.0)
  highlight_color: "#00FF00"  # Color for highlighting detected text
  font_size: 12               # Font size for overlay text
  show_confidence: true       # Show confidence scores
  show_fps: true             # Show FPS counter

# DOAXVV-specific settings
doaxvv:
  # Game window detection
  window_title: "Dead or Alive Xtreme Venus Vacation"
  window_class: null
  
  # UI element detection
  ui_elements:
    menu_buttons:
      - "Home"
      - "Gacha"
      - "Shop"
      - "Event"
      - "Collection"
    
    status_indicators:
      - "Venus Points"
      - "Zack Dollars"
      - "Energy"
      - "Level"
    
    dialog_buttons:
      - "OK"
      - "Cancel"
      - "Yes"
      - "No"
      - "Close"
  
  # Text preprocessing for DOAXVV
  text_preprocessing:
    remove_special_chars: true
    normalize_whitespace: true
    convert_fullwidth: true  # Convert full-width characters to half-width
    
  # Language detection
  language_detection:
    enable: true
    confidence_threshold: 0.8
    fallback_language: "en"
