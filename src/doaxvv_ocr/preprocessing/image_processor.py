"""
Image preprocessing for DOAXVV text recognition.

This module provides image preprocessing functionality to enhance
text recognition accuracy and performance.
"""

import cv2
import numpy as np
from typing import Optional
import logging

from ..config.settings import PipelineConfig


class ImageProcessor:
    """Image preprocessing for text recognition optimization."""
    
    def __init__(self, config: PipelineConfig):
        """
        Initialize image processor.
        
        Args:
            config: Pipeline configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def preprocess(self, image: np.ndarray) -> np.ndarray:
        """
        Apply preprocessing pipeline to image.
        
        Args:
            image: Input image
            
        Returns:
            Preprocessed image
        """
        try:
            processed = image.copy()
            
            # Resize if needed
            if self.config.resize_factor != 1.0:
                processed = self._resize_image(processed, self.config.resize_factor)
            
            # Gaussian blur for noise reduction
            if self.config.gaussian_blur:
                processed = self._apply_gaussian_blur(processed, self.config.blur_kernel_size)
            
            # Contrast enhancement
            if self.config.contrast_enhancement:
                processed = self._enhance_contrast(processed)
            
            # Brightness adjustment
            if self.config.brightness_adjustment != 0.0:
                processed = self._adjust_brightness(processed, self.config.brightness_adjustment)
            
            return processed
            
        except Exception as e:
            self.logger.error(f"Error in image preprocessing: {e}")
            return image
    
    def _resize_image(self, image: np.ndarray, factor: float) -> np.ndarray:
        """Resize image by given factor."""
        if factor <= 0:
            return image
        
        height, width = image.shape[:2]
        new_width = int(width * factor)
        new_height = int(height * factor)
        
        if factor > 1.0:
            # Upscaling - use cubic interpolation
            return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        else:
            # Downscaling - use area interpolation
            return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    def _apply_gaussian_blur(self, image: np.ndarray, kernel_size: int) -> np.ndarray:
        """Apply Gaussian blur for noise reduction."""
        if kernel_size <= 0 or kernel_size % 2 == 0:
            return image
        
        return cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
    
    def _enhance_contrast(self, image: np.ndarray) -> np.ndarray:
        """Enhance image contrast using CLAHE."""
        if len(image.shape) == 3:
            # Convert to LAB color space
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            l_channel, a_channel, b_channel = cv2.split(lab)
            
            # Apply CLAHE to L channel
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l_channel = clahe.apply(l_channel)
            
            # Merge channels and convert back
            lab = cv2.merge([l_channel, a_channel, b_channel])
            return cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        else:
            # Grayscale image
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            return clahe.apply(image)
    
    def _adjust_brightness(self, image: np.ndarray, adjustment: float) -> np.ndarray:
        """Adjust image brightness."""
        if adjustment == 0.0:
            return image
        
        # Convert to float for calculation
        float_image = image.astype(np.float32)
        
        # Apply brightness adjustment
        float_image += adjustment * 255
        
        # Clip values and convert back
        float_image = np.clip(float_image, 0, 255)
        return float_image.astype(np.uint8)
