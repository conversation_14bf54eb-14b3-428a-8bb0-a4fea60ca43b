# Contributing to DOAXVV Text Recognition System

Thank you for your interest in contributing to the DOAXVV Real-Time Text Recognition System! This document provides guidelines for contributing to the project.

## Development Setup

### Prerequisites
- Python 3.8 or higher
- Git
- Virtual environment tool (venv, conda, etc.)

### Setting up Development Environment

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/doaxvv-text-recognition.git
   cd doaxvv-text-recognition
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Development Dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -e .[dev]
   ```

4. **Install Pre-commit Hooks**
   ```bash
   pre-commit install
   ```

## Code Style and Standards

### Python Code Style
- Follow PEP 8 style guidelines
- Use type hints for function parameters and return values
- Maximum line length: 100 characters
- Use descriptive variable and function names

### Code Formatting
We use automated code formatting tools:

```bash
# Format code with black
black src/ tests/ examples/

# Check code style with flake8
flake8 src/ tests/ examples/

# Type checking with mypy
mypy src/
```

### Documentation
- All public functions and classes must have docstrings
- Use Google-style docstrings
- Include type information in docstrings
- Update README.md for significant changes

Example docstring:
```python
def recognize_text(image: np.ndarray, confidence_threshold: float = 0.7) -> List[OCRResult]:
    """
    Recognize text in the provided image.
    
    Args:
        image: Input image as numpy array in BGR format
        confidence_threshold: Minimum confidence score for text detection
        
    Returns:
        List of OCR results with detected text and metadata
        
    Raises:
        ValueError: If image is invalid or empty
    """
```

## Testing

### Running Tests
```bash
# Run all tests
python -m pytest tests/

# Run with coverage
python -m pytest tests/ --cov=src/doaxvv_ocr --cov-report=html

# Run specific test file
python -m pytest tests/test_basic_functionality.py

# Run performance benchmarks
python benchmarks/performance_benchmark.py
```

### Writing Tests
- Write unit tests for all new functionality
- Aim for >90% code coverage
- Use descriptive test names
- Include both positive and negative test cases
- Mock external dependencies (OCR engines, screen capture)

Example test:
```python
def test_text_processor_handles_japanese_text(self):
    """Test that text processor correctly handles Japanese characters."""
    processor = TextProcessor(PipelineConfig())
    japanese_text = "こんにちは世界"
    
    result = processor.process_text(japanese_text)
    
    self.assertIsNotNone(result)
    self.assertIn("こんにちは", result)
```

## Contributing Guidelines

### Issue Reporting
When reporting issues, please include:
- System information (OS, Python version, hardware)
- Steps to reproduce the issue
- Expected vs actual behavior
- Error messages and stack traces
- Screenshots if applicable

### Feature Requests
For new features, please:
- Check existing issues to avoid duplicates
- Describe the use case and motivation
- Provide implementation suggestions if possible
- Consider backward compatibility

### Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write code following style guidelines
   - Add tests for new functionality
   - Update documentation as needed

3. **Test Changes**
   ```bash
   python -m pytest tests/
   python benchmarks/performance_benchmark.py
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new OCR engine support"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

### Commit Message Format
Use conventional commit format:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `test:` for test additions/changes
- `refactor:` for code refactoring
- `perf:` for performance improvements

## Areas for Contribution

### High Priority
- **OCR Engine Optimization**: Improve accuracy and performance
- **Platform Support**: Better Windows/macOS/Linux compatibility
- **GPU Acceleration**: Optimize CUDA and Metal support
- **DOAXVV Integration**: Game-specific optimizations

### Medium Priority
- **UI Improvements**: Better configuration interface
- **Documentation**: More examples and tutorials
- **Testing**: Increase test coverage
- **Performance**: Memory usage optimization

### Low Priority
- **Additional OCR Engines**: Support for more engines
- **Export Features**: Save/load recognition results
- **Plugins**: Extensible architecture
- **Localization**: Multi-language support

## Development Workflow

### Branch Strategy
- `main`: Stable release branch
- `develop`: Development integration branch
- `feature/*`: Feature development branches
- `hotfix/*`: Critical bug fixes

### Release Process
1. Feature development in feature branches
2. Merge to develop branch for integration testing
3. Create release branch from develop
4. Final testing and bug fixes
5. Merge to main and tag release

## Performance Considerations

### Optimization Guidelines
- Profile code before optimizing
- Focus on hot paths (OCR processing, image preprocessing)
- Consider memory usage and garbage collection
- Test on target hardware configurations

### Benchmarking
- Run benchmarks before and after changes
- Document performance impact
- Maintain performance regression tests

## Code Review Guidelines

### For Reviewers
- Check code style and standards compliance
- Verify test coverage and quality
- Test functionality manually if needed
- Provide constructive feedback
- Approve only when confident in changes

### For Contributors
- Respond to feedback promptly
- Make requested changes in separate commits
- Update PR description if scope changes
- Ensure CI passes before requesting review

## Community Guidelines

### Code of Conduct
- Be respectful and inclusive
- Focus on constructive feedback
- Help newcomers learn and contribute
- Maintain professional communication

### Getting Help
- Check documentation first
- Search existing issues
- Ask questions in discussions
- Join community chat if available

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes for significant contributions
- Project documentation

## License

By contributing, you agree that your contributions will be licensed under the same license as the project (MIT License).

## Questions?

If you have questions about contributing, please:
1. Check this document and project documentation
2. Search existing issues and discussions
3. Create a new issue with the "question" label
4. Contact maintainers directly if needed

Thank you for contributing to the DOAXVV Text Recognition System!
