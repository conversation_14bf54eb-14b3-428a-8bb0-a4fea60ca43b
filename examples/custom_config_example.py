#!/usr/bin/env python3
"""
Custom configuration example for DOAXVV Text Recognition System.

This example shows how to create and use custom configurations
for different use cases and performance requirements.
"""

import sys
import time
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from doaxvv_ocr import DOAXVVTextRecognizer, Config


def create_high_performance_config():
    """Create a configuration optimized for high performance."""
    config = Config()
    
    # Optimize for speed
    config.performance.target_fps = 30
    config.performance.max_processing_time = 0.05  # 50ms
    config.performance.enable_multithreading = True
    config.performance.thread_pool_size = 6
    
    # Use fastest capture method
    config.capture.method = "mss"
    config.capture.fps_limit = 30
    
    # Optimize OCR settings
    config.ocr.primary_engine = "paddleocr"
    config.ocr.paddleocr_settings["use_gpu"] = True
    config.ocr.paddleocr_settings["use_angle_cls"] = False  # Disable for speed
    
    # Focus on specific regions
    config.pipeline.enable_roi_detection = True
    config.pipeline.roi_regions = {
        "menu_bar": {"x": 0, "y": 0, "width": 1280, "height": 100},
        "status_area": {"x": 0, "y": 680, "width": 1280, "height": 100}
    }
    
    return config


def create_accuracy_config():
    """Create a configuration optimized for accuracy."""
    config = Config()
    
    # Optimize for accuracy
    config.performance.target_fps = 10
    config.performance.max_processing_time = 0.2  # 200ms
    
    # Use multiple OCR engines
    config.ocr.primary_engine = "paddleocr"
    config.ocr.secondary_engine = "easyocr"
    config.ocr.fallback_engine = "tesseract"
    
    # Enable all preprocessing
    config.pipeline.gaussian_blur = True
    config.pipeline.contrast_enhancement = True
    config.pipeline.resize_factor = 1.5  # Upscale for better recognition
    
    # Lower confidence thresholds to catch more text
    config.pipeline.min_confidence = 0.5
    
    return config


def create_doaxvv_optimized_config():
    """Create a configuration specifically optimized for DOAXVV."""
    config = Config()
    
    # DOAXVV-specific settings
    config.performance.target_fps = 20
    config.performance.max_processing_time = 0.1
    
    # Screen capture for typical DOAXVV resolution
    config.capture.region = {"x": 0, "y": 0, "width": 1280, "height": 720}
    
    # DOAXVV UI regions
    config.pipeline.roi_regions = {
        "top_menu": {"x": 0, "y": 0, "width": 1280, "height": 80},
        "left_menu": {"x": 0, "y": 80, "width": 200, "height": 640},
        "dialog_area": {"x": 200, "y": 150, "width": 880, "height": 420},
        "bottom_status": {"x": 0, "y": 640, "width": 1280, "height": 80},
        "notification": {"x": 400, "y": 100, "width": 480, "height": 200}
    }
    
    # Japanese + English support
    config.ocr.paddleocr_settings["lang"] = "japan"
    config.ocr.easyocr_settings["languages"] = ["en", "ja"]
    
    # Text filtering for DOAXVV
    config.pipeline.min_text_length = 1  # Allow single characters
    config.pipeline.filter_patterns = [
        r"^\d+$",  # Numbers only
        r"^[A-Za-z\s]+$",  # English text
        r"^[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\s]+$"  # Japanese text
    ]
    
    return config


def run_with_config(config, name, duration=10):
    """Run recognition with a specific configuration."""
    print(f"\n{'='*50}")
    print(f"Running with {name} configuration")
    print(f"{'='*50}")
    
    try:
        recognizer = DOAXVVTextRecognizer()
        recognizer.config = config
        
        # Simple text callback
        def text_callback(results):
            for result in results:
                print(f"[{result.confidence:.2f}] {result.text}")
        
        # Performance callback
        def perf_callback(metrics):
            print(f"FPS: {metrics.fps:.1f} | Time: {metrics.avg_processing_time*1000:.1f}ms")
        
        recognizer.set_text_callback(text_callback)
        recognizer.set_performance_callback(perf_callback)
        
        recognizer.start_realtime_recognition()
        
        # Run for specified duration
        time.sleep(duration)
        
        recognizer.stop_realtime_recognition()
        
        # Print results
        final_metrics = recognizer.get_performance_metrics()
        print(f"\nResults for {name}:")
        print(f"Average FPS: {final_metrics.fps:.2f}")
        print(f"Average processing time: {final_metrics.avg_processing_time*1000:.2f}ms")
        print(f"Total frames: {final_metrics.frame_count}")
        
    except Exception as e:
        print(f"Error with {name} config: {e}")


def main():
    """Main example function."""
    print("DOAXVV Text Recognition - Custom Configuration Example")
    print("This example demonstrates different configuration presets")
    
    # Create different configurations
    configs = {
        "High Performance": create_high_performance_config(),
        "High Accuracy": create_accuracy_config(),
        "DOAXVV Optimized": create_doaxvv_optimized_config()
    }
    
    # Save configurations to files
    for name, config in configs.items():
        filename = f"config_{name.lower().replace(' ', '_')}.yaml"
        config.save_to_file(filename)
        print(f"Saved {name} configuration to {filename}")
    
    print("\nYou can now use these configurations with:")
    print("python -m doaxvv_ocr.cli realtime -c config_high_performance.yaml")
    print("python -m doaxvv_ocr.cli realtime -c config_high_accuracy.yaml")
    print("python -m doaxvv_ocr.cli realtime -c config_doaxvv_optimized.yaml")
    
    # Optionally run a quick test with each config
    response = input("\nWould you like to test each configuration? (y/n): ")
    if response.lower() == 'y':
        for name, config in configs.items():
            run_with_config(config, name, duration=5)


if __name__ == "__main__":
    main()
