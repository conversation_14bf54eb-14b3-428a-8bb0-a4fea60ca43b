#!/usr/bin/env python3
"""
Basic usage example for DOAXVV Text Recognition System.

This example demonstrates how to use the text recognition system
for real-time text detection in DOAXVV.
"""

import time
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from doaxvv_ocr import DOAXVVTextRecognizer, Config
from doaxvv_ocr.core.recognizer import RecognitionResult, PerformanceMetrics


def on_text_detected(results):
    """
    Callback function called when text is detected.
    
    Args:
        results: List of RecognitionResult objects
    """
    for result in results:
        print(f"[{result.confidence:.2f}] {result.text}")
        if result.bbox:
            x, y, w, h = result.bbox
            print(f"  Location: ({x}, {y}) Size: {w}x{h}")


def on_performance_update(metrics):
    """
    Callback function called with performance metrics.
    
    Args:
        metrics: PerformanceMetrics object
    """
    print(f"FPS: {metrics.fps:.1f} | "
          f"Processing: {metrics.avg_processing_time*1000:.1f}ms | "
          f"Frames: {metrics.frame_count}")


def main():
    """Main example function."""
    print("DOAXVV Text Recognition - Basic Usage Example")
    print("=" * 50)
    
    try:
        # Initialize the recognizer with default configuration
        recognizer = DOAXVVTextRecognizer()
        
        # Set up callbacks
        recognizer.set_text_callback(on_text_detected)
        recognizer.set_performance_callback(on_performance_update)
        
        # Start real-time recognition
        print("Starting real-time text recognition...")
        print("Make sure DOAXVV is running and visible on screen.")
        print("Press Ctrl+C to stop.\n")
        
        recognizer.start_realtime_recognition()
        
        # Run for 30 seconds or until interrupted
        start_time = time.time()
        try:
            while time.time() - start_time < 30:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nStopping...")
        
        # Stop recognition
        recognizer.stop_realtime_recognition()
        
        # Print final statistics
        final_metrics = recognizer.get_performance_metrics()
        print(f"\nFinal Statistics:")
        print(f"Total frames processed: {final_metrics.frame_count}")
        print(f"Average FPS: {final_metrics.fps:.2f}")
        print(f"Total runtime: {final_metrics.total_time:.2f} seconds")
        print(f"Average processing time: {final_metrics.avg_processing_time*1000:.2f}ms")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
