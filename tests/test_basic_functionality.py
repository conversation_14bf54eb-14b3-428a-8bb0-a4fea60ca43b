#!/usr/bin/env python3
"""
Basic functionality tests for DOAXVV Text Recognition System.

This module contains unit tests for core functionality including
configuration, screen capture, OCR engines, and text processing.
"""

import unittest
import sys
import os
from pathlib import Path
import numpy as np
import cv2

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from doaxvv_ocr.config.settings import Config, CaptureConfig, OCRConfig, PipelineConfig
from doaxvv_ocr.capture.screen_capture import ScreenCaptureFactory
from doaxvv_ocr.capture.mss_capture import MSSCapture
from doaxvv_ocr.preprocessing.image_processor import ImageProcessor
from doaxvv_ocr.postprocessing.text_processor import TextProcessor


class TestConfiguration(unittest.TestCase):
    """Test configuration management."""
    
    def test_default_config_creation(self):
        """Test creating default configuration."""
        config = Config()
        
        self.assertIsInstance(config.capture, CaptureConfig)
        self.assertIsInstance(config.ocr, OCRConfig)
        self.assertIsInstance(config.pipeline, PipelineConfig)
        
        # Test default values
        self.assertEqual(config.capture.method, "auto")
        self.assertEqual(config.ocr.primary_engine, "paddleocr")
        self.assertEqual(config.pipeline.min_confidence, 0.7)
    
    def test_config_serialization(self):
        """Test configuration save/load functionality."""
        config = Config()
        
        # Modify some values
        config.capture.fps_limit = 25
        config.ocr.primary_engine = "easyocr"
        config.pipeline.min_confidence = 0.8
        
        # Test dictionary conversion
        config_dict = config.get_config_dict()
        
        self.assertEqual(config_dict['capture']['fps_limit'], 25)
        self.assertEqual(config_dict['ocr']['primary_engine'], "easyocr")
        self.assertEqual(config_dict['pipeline']['min_confidence'], 0.8)


class TestScreenCapture(unittest.TestCase):
    """Test screen capture functionality."""
    
    def test_capture_factory(self):
        """Test screen capture factory."""
        config = CaptureConfig()
        
        # Test factory creation
        capture = ScreenCaptureFactory.create(config)
        self.assertIsNotNone(capture)
        
        # Test available methods
        methods = ScreenCaptureFactory.get_available_methods()
        self.assertIn("mss", methods)
        self.assertTrue(methods["mss"])  # MSS should always be available
    
    def test_mss_capture_initialization(self):
        """Test MSS capture initialization."""
        config = CaptureConfig()
        capture = MSSCapture(config)
        
        # Test initialization
        success = capture.initialize()
        self.assertTrue(success)
        self.assertTrue(capture.is_initialized())
        
        # Test screen size
        width, height = capture.get_screen_size()
        self.assertGreater(width, 0)
        self.assertGreater(height, 0)
        
        # Cleanup
        capture.cleanup()
        self.assertFalse(capture.is_initialized())
    
    def test_screen_capture_basic(self):
        """Test basic screen capture functionality."""
        config = CaptureConfig()
        capture = ScreenCaptureFactory.create(config)
        
        try:
            with capture:
                # Test capture
                frame = capture.capture()
                
                if frame is not None:  # May fail in headless environments
                    self.assertIsInstance(frame, np.ndarray)
                    self.assertEqual(len(frame.shape), 3)  # Should be color image
                    self.assertGreater(frame.shape[0], 0)  # Height > 0
                    self.assertGreater(frame.shape[1], 0)  # Width > 0
                    self.assertEqual(frame.shape[2], 3)    # BGR channels
        except Exception as e:
            # Skip test in headless environments
            self.skipTest(f"Screen capture not available: {e}")


class TestImageProcessing(unittest.TestCase):
    """Test image preprocessing functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = PipelineConfig()
        self.processor = ImageProcessor(self.config)
        
        # Create test image
        self.test_image = np.ones((100, 200, 3), dtype=np.uint8) * 128
        cv2.putText(self.test_image, "TEST TEXT", (10, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    def test_basic_preprocessing(self):
        """Test basic image preprocessing."""
        processed = self.processor.preprocess(self.test_image)
        
        self.assertIsInstance(processed, np.ndarray)
        self.assertEqual(processed.shape, self.test_image.shape)
    
    def test_resize_functionality(self):
        """Test image resizing."""
        self.config.resize_factor = 2.0
        processor = ImageProcessor(self.config)
        
        processed = processor.preprocess(self.test_image)
        
        # Should be 2x larger
        expected_height = self.test_image.shape[0] * 2
        expected_width = self.test_image.shape[1] * 2
        
        self.assertEqual(processed.shape[0], expected_height)
        self.assertEqual(processed.shape[1], expected_width)
    
    def test_blur_functionality(self):
        """Test Gaussian blur."""
        self.config.gaussian_blur = True
        self.config.blur_kernel_size = 5
        processor = ImageProcessor(self.config)
        
        processed = processor.preprocess(self.test_image)
        
        # Blurred image should be different from original
        self.assertFalse(np.array_equal(processed, self.test_image))


class TestTextProcessing(unittest.TestCase):
    """Test text postprocessing functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = PipelineConfig()
        self.processor = TextProcessor(self.config)
    
    def test_basic_text_cleaning(self):
        """Test basic text cleaning."""
        # Test with messy text
        messy_text = "  Hello   World!  "
        cleaned = self.processor.process_text(messy_text)
        
        self.assertEqual(cleaned, "Hello World!")
    
    def test_doaxvv_text_patterns(self):
        """Test DOAXVV-specific text processing."""
        # Test Venus Points recognition
        text = "Venus Pts: 1500"
        processed = self.processor.process_text(text)
        
        self.assertIn("Points", processed)
    
    def test_text_validation(self):
        """Test text validation."""
        # Valid text
        self.assertTrue(self.processor._is_valid_text("Hello World"))
        
        # Too short
        self.config.min_text_length = 5
        processor = TextProcessor(self.config)
        self.assertFalse(processor._is_valid_text("Hi"))
        
        # Too long
        self.config.max_text_length = 10
        processor = TextProcessor(self.config)
        self.assertFalse(processor._is_valid_text("This is a very long text"))
    
    def test_number_extraction(self):
        """Test number extraction from text."""
        text = "Venus Points: 1500, Zack Dollars: 2500"
        numbers = self.processor.extract_numbers(text)
        
        self.assertEqual(numbers, [1500, 2500])
    
    def test_menu_item_detection(self):
        """Test menu item detection."""
        self.assertTrue(self.processor.is_menu_item("Gacha"))
        self.assertTrue(self.processor.is_menu_item("Venus Points"))
        self.assertFalse(self.processor.is_menu_item("Random Text"))
    
    def test_text_categorization(self):
        """Test text categorization."""
        self.assertEqual(self.processor.get_text_category("Gacha"), "menu_items")
        self.assertEqual(self.processor.get_text_category("OK"), "dialog_buttons")
        self.assertEqual(self.processor.get_text_category("12345"), "number")
        self.assertEqual(self.processor.get_text_category("1500 points"), "status_value")


class TestIntegration(unittest.TestCase):
    """Integration tests for combined functionality."""
    
    def test_config_pipeline_integration(self):
        """Test configuration with pipeline components."""
        config = Config()
        
        # Test that components can be created with config
        image_processor = ImageProcessor(config.pipeline)
        text_processor = TextProcessor(config.pipeline)
        
        self.assertIsNotNone(image_processor)
        self.assertIsNotNone(text_processor)
    
    def test_end_to_end_image_processing(self):
        """Test end-to-end image processing pipeline."""
        config = Config()
        image_processor = ImageProcessor(config.pipeline)
        text_processor = TextProcessor(config.pipeline)
        
        # Create test image with text
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "Venus Points: 1500", (10, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        
        # Process image
        processed_image = image_processor.preprocess(test_image)
        
        # Simulate OCR result
        raw_text = "Venus Pts: 1500"
        processed_text = text_processor.process_text(raw_text)
        
        self.assertIn("Points", processed_text)
        self.assertIn("1500", processed_text)


def create_test_suite():
    """Create test suite with all test cases."""
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTest(unittest.makeSuite(TestConfiguration))
    suite.addTest(unittest.makeSuite(TestScreenCapture))
    suite.addTest(unittest.makeSuite(TestImageProcessing))
    suite.addTest(unittest.makeSuite(TestTextProcessing))
    suite.addTest(unittest.makeSuite(TestIntegration))
    
    return suite


if __name__ == "__main__":
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    suite = create_test_suite()
    result = runner.run(suite)
    
    # Exit with error code if tests failed
    sys.exit(0 if result.wasSuccessful() else 1)
