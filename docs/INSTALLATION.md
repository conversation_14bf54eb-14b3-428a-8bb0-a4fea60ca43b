# Installation Guide

This guide provides detailed instructions for installing the DOAXVV Real-Time Text Recognition System on different platforms.

## System Requirements

### Minimum Requirements
- **OS**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **RAM**: 8GB minimum, 16GB recommended
- **CPU**: Intel i5 or AMD Ryzen 5 equivalent
- **Python**: 3.8 or higher

### Recommended Requirements
- **RAM**: 16GB or more
- **GPU**: NVIDIA GPU with CUDA support or Apple Silicon with Metal
- **CPU**: Intel i7/i9 or AMD Ryzen 7/9
- **Storage**: 5GB free space for models and dependencies

## Platform-Specific Installation

### Windows 10/11

#### Prerequisites
1. **Install Python 3.8+**
   ```bash
   # Download from python.org or use Microsoft Store
   python --version  # Verify installation
   ```

2. **Install Visual Studio Build Tools** (for some dependencies)
   - Download from Microsoft Visual Studio website
   - Install "C++ build tools" workload

3. **Install Tesseract OCR** (optional but recommended)
   ```bash
   # Using chocolatey
   choco install tesseract
   
   # Or download installer from GitHub releases
   # https://github.com/UB-Mannheim/tesseract/wiki
   ```

#### Installation Steps
```bash
# Clone the repository
git clone <repository-url>
cd text_recognition

# Create virtual environment
python -m venv venv
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install the package
pip install -e .

# Test installation
python -m doaxvv_ocr.cli test-capture
```

### macOS

#### Prerequisites
1. **Install Homebrew**
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. **Install Python 3.8+**
   ```bash
   brew install python@3.11
   ```

3. **Install Tesseract OCR**
   ```bash
   brew install tesseract
   brew install tesseract-lang  # For additional languages
   ```

#### Installation Steps
```bash
# Clone the repository
git clone <repository-url>
cd text_recognition

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install the package
pip install -e .

# Test installation
python -m doaxvv_ocr.cli test-capture
```

### Linux (Ubuntu/Debian)

#### Prerequisites
```bash
# Update package list
sudo apt update

# Install Python and development tools
sudo apt install python3.8 python3.8-venv python3.8-dev
sudo apt install build-essential cmake pkg-config

# Install system dependencies
sudo apt install libopencv-dev python3-opencv
sudo apt install tesseract-ocr tesseract-ocr-jpn tesseract-ocr-eng

# Install additional libraries
sudo apt install libx11-dev libxrandr-dev libxinerama-dev libxcursor-dev libxi-dev
```

#### Installation Steps
```bash
# Clone the repository
git clone <repository-url>
cd text_recognition

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install the package
pip install -e .

# Test installation
python -m doaxvv_ocr.cli test-capture
```

## GPU Acceleration Setup

### NVIDIA CUDA (Windows/Linux)

1. **Install CUDA Toolkit**
   - Download from NVIDIA website (CUDA 11.8 or 12.x)
   - Follow platform-specific installation instructions

2. **Install cuDNN**
   - Download from NVIDIA Developer website
   - Extract and copy files to CUDA installation directory

3. **Install GPU-enabled packages**
   ```bash
   # Install PyTorch with CUDA support
   pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
   
   # Install ONNX Runtime with GPU support
   pip uninstall onnxruntime
   pip install onnxruntime-gpu
   ```

4. **Verify GPU setup**
   ```bash
   python -c "import torch; print(torch.cuda.is_available())"
   ```

### Apple Silicon (macOS)

GPU acceleration is automatically enabled on Apple Silicon Macs through Metal Performance Shaders.

```bash
# Verify Metal support
python -c "import torch; print(torch.backends.mps.is_available())"
```

## Verification

### Test Basic Functionality
```bash
# Test screen capture
python -m doaxvv_ocr.cli test-capture

# Test OCR engines
python -m doaxvv_ocr.cli benchmark

# Run basic recognition test
python examples/basic_usage.py
```

### Performance Verification
```bash
# Run performance benchmark
python -m doaxvv_ocr.cli benchmark

# Expected results:
# - CPU-only: 15-20 FPS, 50-70ms processing time
# - GPU-accelerated: 25-35 FPS, 30-50ms processing time
```

## Troubleshooting

### Common Issues

#### "No module named 'cv2'"
```bash
pip uninstall opencv-python opencv-contrib-python
pip install opencv-python==********
```

#### "Tesseract not found"
- **Windows**: Add Tesseract installation directory to PATH
- **macOS**: `brew install tesseract`
- **Linux**: `sudo apt install tesseract-ocr`

#### "CUDA out of memory"
```python
# Reduce batch size or image resolution in config
config.pipeline.resize_factor = 0.8
config.performance.enable_gpu_acceleration = False
```

#### Low FPS Performance
1. Check GPU acceleration is enabled
2. Reduce target FPS in configuration
3. Enable ROI detection to process smaller regions
4. Use faster OCR engine (PaddleOCR over Tesseract)

### Getting Help

1. **Check logs**: Enable verbose logging with `-v` flag
2. **System info**: Run `python -m doaxvv_ocr.cli system-info`
3. **GitHub Issues**: Report bugs with system information and logs
4. **Documentation**: Check docs/ directory for detailed guides

## Next Steps

After successful installation:

1. **Configure for DOAXVV**: See [CONFIGURATION.md](CONFIGURATION.md)
2. **Run examples**: Check `examples/` directory
3. **Performance tuning**: See [PERFORMANCE.md](PERFORMANCE.md)
4. **Custom integration**: See [API.md](API.md)
