# Usage Guide

This guide explains how to use the DOAXVV Real-Time Text Recognition System for various scenarios.

## Quick Start

### Command Line Interface

The easiest way to get started is using the command-line interface:

```bash
# Start real-time recognition
python -m doaxvv_ocr.cli realtime

# Run for specific duration
python -m doaxvv_ocr.cli realtime -d 60

# Use custom configuration
python -m doaxvv_ocr.cli realtime -c my_config.yaml
```

### Python API

For programmatic use:

```python
from doaxvv_ocr import DOAXVVTextRecognizer

# Initialize recognizer
recognizer = DOAXVVTextRecognizer()

# Set up callbacks
def on_text_detected(results):
    for result in results:
        print(f"Detected: {result.text} (confidence: {result.confidence:.2f})")

recognizer.set_text_callback(on_text_detected)

# Start recognition
recognizer.start_realtime_recognition()

# Your application logic here
import time
time.sleep(30)  # Run for 30 seconds

# Stop recognition
recognizer.stop_realtime_recognition()
```

## Configuration

### Basic Configuration

Create a configuration file to customize behavior:

```yaml
# config.yaml
capture:
  method: "auto"
  monitor: 0
  fps_limit: 30

ocr:
  primary_engine: "paddleocr"
  secondary_engine: "easyocr"

performance:
  target_fps: 20
  enable_gpu_acceleration: true

pipeline:
  enable_roi_detection: true
  min_confidence: 0.7
```

### DOAXVV-Specific Configuration

For optimal DOAXVV performance:

```yaml
# doaxvv_config.yaml
capture:
  region: {x: 0, y: 0, width: 1280, height: 720}

pipeline:
  roi_regions:
    menu_area: {x: 0, y: 0, width: 1280, height: 80}
    dialog_area: {x: 200, y: 150, width: 880, height: 420}
    status_bar: {x: 0, y: 640, width: 1280, height: 80}

ocr:
  paddleocr_settings:
    lang: "japan"
  easyocr_settings:
    languages: ["en", "ja"]
```

## Use Cases

### 1. Menu Navigation Detection

Detect when specific menu items appear:

```python
from doaxvv_ocr import DOAXVVTextRecognizer

recognizer = DOAXVVTextRecognizer()

menu_items = ["Gacha", "Shop", "Event", "Collection"]

def check_menu_items(results):
    detected_menus = []
    for result in results:
        for menu in menu_items:
            if menu.lower() in result.text.lower():
                detected_menus.append(menu)
    
    if detected_menus:
        print(f"Menu items detected: {detected_menus}")

recognizer.set_text_callback(check_menu_items)
recognizer.start_realtime_recognition()
```

### 2. Status Monitoring

Monitor game status values:

```python
import re
from doaxvv_ocr import DOAXVVTextRecognizer

def monitor_status(results):
    for result in results:
        text = result.text
        
        # Check for Venus Points
        vp_match = re.search(r'(\d+)\s*(?:Venus\s*)?Points?', text, re.IGNORECASE)
        if vp_match:
            points = int(vp_match.group(1))
            print(f"Venus Points: {points}")
        
        # Check for Zack Dollars
        zd_match = re.search(r'(\d+)\s*(?:Zack\s*)?Dollars?', text, re.IGNORECASE)
        if zd_match:
            dollars = int(zd_match.group(1))
            print(f"Zack Dollars: {dollars}")
        
        # Check for Energy
        energy_match = re.search(r'Energy:?\s*(\d+)', text, re.IGNORECASE)
        if energy_match:
            energy = int(energy_match.group(1))
            print(f"Energy: {energy}")

recognizer = DOAXVVTextRecognizer()
recognizer.set_text_callback(monitor_status)
recognizer.start_realtime_recognition()
```

### 3. Dialog Detection

Detect and respond to dialog boxes:

```python
from doaxvv_ocr import DOAXVVTextRecognizer

dialog_keywords = ["OK", "Cancel", "Yes", "No", "Confirm", "Close"]

def detect_dialogs(results):
    for result in results:
        for keyword in dialog_keywords:
            if keyword.lower() in result.text.lower():
                print(f"Dialog detected: {keyword} at {result.bbox}")
                # You could trigger automated responses here

recognizer = DOAXVVTextRecognizer()
recognizer.set_text_callback(detect_dialogs)
recognizer.start_realtime_recognition()
```

### 4. Event Notification Monitoring

Monitor for special events or notifications:

```python
from doaxvv_ocr import DOAXVVTextRecognizer
import datetime

event_keywords = ["Event", "Limited", "Special", "Bonus", "Reward"]

def monitor_events(results):
    for result in results:
        text = result.text.lower()
        for keyword in event_keywords:
            if keyword.lower() in text:
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{timestamp}] Event notification: {result.text}")

recognizer = DOAXVVTextRecognizer()
recognizer.set_text_callback(monitor_events)
recognizer.start_realtime_recognition()
```

## Performance Optimization

### Region of Interest (ROI) Configuration

Focus on specific screen areas for better performance:

```python
from doaxvv_ocr import Config

config = Config()
config.pipeline.enable_roi_detection = True
config.pipeline.roi_regions = {
    "top_menu": {"x": 0, "y": 0, "width": 1280, "height": 100},
    "bottom_status": {"x": 0, "y": 620, "width": 1280, "height": 100}
}

recognizer = DOAXVVTextRecognizer()
recognizer.config = config
```

### Performance Monitoring

Track system performance:

```python
from doaxvv_ocr import DOAXVVTextRecognizer

def performance_callback(metrics):
    print(f"FPS: {metrics.fps:.1f}")
    print(f"Processing Time: {metrics.avg_processing_time*1000:.1f}ms")
    print(f"CPU Usage: {metrics.cpu_usage:.1f}%")
    print(f"Memory Usage: {metrics.memory_usage:.1f}MB")

recognizer = DOAXVVTextRecognizer()
recognizer.set_performance_callback(performance_callback)
```

## Advanced Usage

### Custom OCR Engine Selection

Choose specific OCR engines for different scenarios:

```python
from doaxvv_ocr.ocr import OCRManager
from doaxvv_ocr.config.settings import OCRConfig

# High-speed configuration
speed_config = OCRConfig()
speed_config.primary_engine = "paddleocr"
speed_config.paddleocr_settings["use_angle_cls"] = False

# High-accuracy configuration  
accuracy_config = OCRConfig()
accuracy_config.primary_engine = "easyocr"
accuracy_config.secondary_engine = "paddleocr"
accuracy_config.fallback_engine = "tesseract"
```

### Batch Processing

Process multiple images:

```python
from doaxvv_ocr import DOAXVVTextRecognizer
import cv2

recognizer = DOAXVVTextRecognizer()

# Process a list of images
image_paths = ["screenshot1.png", "screenshot2.png", "screenshot3.png"]

for path in image_paths:
    image = cv2.imread(path)
    results = recognizer.recognize_frame(image)
    
    print(f"Results for {path}:")
    for result in results:
        print(f"  {result.text} (confidence: {result.confidence:.2f})")
```

## Integration Examples

### Discord Bot Integration

```python
import discord
from doaxvv_ocr import DOAXVVTextRecognizer

class DOAXVVBot(discord.Client):
    def __init__(self):
        super().__init__()
        self.recognizer = DOAXVVTextRecognizer()
        self.recognizer.set_text_callback(self.on_text_detected)
    
    def on_text_detected(self, results):
        # Send important detections to Discord channel
        for result in results:
            if "Event" in result.text or "Limited" in result.text:
                # Send notification to Discord
                pass

bot = DOAXVVBot()
```

### Web API Integration

```python
from flask import Flask, jsonify
from doaxvv_ocr import DOAXVVTextRecognizer

app = Flask(__name__)
recognizer = DOAXVVTextRecognizer()

latest_results = []

def update_results(results):
    global latest_results
    latest_results = results

recognizer.set_text_callback(update_results)
recognizer.start_realtime_recognition()

@app.route('/api/text')
def get_latest_text():
    return jsonify([{
        'text': r.text,
        'confidence': r.confidence,
        'bbox': r.bbox
    } for r in latest_results])

if __name__ == '__main__':
    app.run(debug=True)
```

## Troubleshooting

### Common Issues

1. **Low FPS**: Reduce target FPS, enable ROI detection, use GPU acceleration
2. **Poor Accuracy**: Increase confidence thresholds, use multiple OCR engines
3. **High CPU Usage**: Enable GPU acceleration, reduce processing frequency
4. **Memory Leaks**: Ensure proper cleanup, limit buffer sizes

### Debug Mode

Enable detailed logging:

```bash
python -m doaxvv_ocr.cli realtime -v
```

Or in Python:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

recognizer = DOAXVVTextRecognizer()
```
