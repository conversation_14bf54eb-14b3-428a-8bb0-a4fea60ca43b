# Core OCR engines
paddlepaddle>=2.5.0
paddleocr>=2.7.0
easyocr>=1.7.0
pytesseract>=0.3.10

# Image processing and computer vision
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0
scipy>=1.11.0

# Screen capture dependencies
mss>=9.0.1  # Cross-platform screen capture
pyautogui>=0.9.54  # Additional screen utilities

# Platform-specific screen capture (Windows)
pywin32>=306; sys_platform == "win32"
pygetwindow>=0.0.9; sys_platform == "win32"

# Platform-specific screen capture (macOS)
pyobjc-framework-Quartz>=9.2; sys_platform == "darwin"
pyobjc-framework-CoreGraphics>=9.2; sys_platform == "darwin"

# Platform-specific screen capture (Linux)
python-xlib>=0.33; sys_platform == "linux"
pycairo>=1.24.0; sys_platform == "linux"

# GPU acceleration
torch>=2.0.0
torchvision>=0.15.0
onnxruntime-gpu>=1.15.0  # For ONNX model inference

# Performance and utilities
psutil>=5.9.0  # System monitoring
threading-timer>=0.1.0
concurrent-futures>=3.1.1
multiprocessing-logging>=0.3.4

# Configuration and data handling
pyyaml>=6.0
configparser>=5.3.0
json5>=0.9.14
toml>=0.10.2

# Logging and monitoring
loguru>=0.7.0
tqdm>=4.65.0

# GUI framework (optional)
tkinter-tooltip>=2.0.0
customtkinter>=5.2.0

# Development and testing
pytest>=7.4.0
pytest-benchmark>=4.0.0
pytest-cov>=4.1.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# Benchmarking and profiling
memory-profiler>=0.61.0
line-profiler>=4.1.0
py-spy>=0.3.14
